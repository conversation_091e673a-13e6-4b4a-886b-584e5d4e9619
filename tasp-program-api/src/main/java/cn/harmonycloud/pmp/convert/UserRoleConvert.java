package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.entity.UserRole;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserRoleConvert {

    UserRoleConvert INSTANCE = Mappers.getMapper(UserRoleConvert.class);
    List<UserRoleVo> do2Vo(List<UserRole> userRoleList);
}
