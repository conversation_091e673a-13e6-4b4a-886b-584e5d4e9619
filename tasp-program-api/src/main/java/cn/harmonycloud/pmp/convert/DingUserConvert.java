package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.DingUserDto;
import cn.harmonycloud.pmp.model.entity.User;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DingUserConvert {
    DingUserConvert INSTANCE = Mappers.getMapper(DingUserConvert.class);

    List<User> dto2userDo(List<DingUserDto> dingUserDtoList);
}
