package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.OrganizationRoleDto;
import cn.harmonycloud.pmp.model.entity.OrganizationRole;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface OrganizationRoleConvert {
    OrganizationRoleConvert INSTANCE = Mappers.getMapper(OrganizationRoleConvert.class);

    OrganizationRole dto2do(OrganizationRoleDto organizationRoleDto);

}