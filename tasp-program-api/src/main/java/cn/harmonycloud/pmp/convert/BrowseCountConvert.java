package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.BrowseAddCount;
import cn.harmonycloud.pmp.model.entity.BrowseCount;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface BrowseCountConvert {

    BrowseCountConvert INSTANCE = Mappers.getMapper(BrowseCountConvert.class);
    BrowseCount dto2Do(BrowseAddCount browseAddCount);
}
