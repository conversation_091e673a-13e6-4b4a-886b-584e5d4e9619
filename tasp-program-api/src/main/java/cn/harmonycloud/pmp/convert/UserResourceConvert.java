package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.UserResource;
import cn.harmonycloud.pmp.model.entity.UserOrganization;
import cn.harmonycloud.pmp.model.entity.UserRole;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserResourceConvert {

    UserResourceConvert INSTANCE = Mappers.getMapper(UserResourceConvert.class);
    List<UserOrganization> dos2UserOrgans(List<UserResource> userResources);
    List<UserRole> dos2UserRoles(List<UserResource> userResources);
}
