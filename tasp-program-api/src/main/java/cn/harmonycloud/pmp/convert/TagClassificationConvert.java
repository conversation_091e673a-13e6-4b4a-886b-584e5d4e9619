package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.entity.Tag;
import cn.harmonycloud.pmp.model.entity.TagClassification;
import cn.harmonycloud.pmp.model.entity.TagsByClassificationCode;
import cn.harmonycloud.pmp.model.vo.TagClassificationVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface TagClassificationConvert {

    TagClassificationConvert INSTANCE = Mappers.getMapper(TagClassificationConvert.class);
    List<TagClassificationVo> tags2Vos(List<Tag> tags);
    List<TagClassificationVo> dos2Vos(List<TagClassification> tagClassifications);
    TagClassification dto2do(TagsByClassificationCode tagsByClassificationCode);
}
