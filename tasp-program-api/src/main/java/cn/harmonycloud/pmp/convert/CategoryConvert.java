package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.CategoryAddDto;
import cn.harmonycloud.pmp.model.dto.CategoryUpdateDto;
import cn.harmonycloud.pmp.model.entity.Category;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface CategoryConvert {
    CategoryConvert INSTANCE = Mappers.getMapper(CategoryConvert.class);
    Category dto2Do(CategoryAddDto categoryAddDto);
    Category dto2Do(CategoryUpdateDto categoryUpdateDto);
}
