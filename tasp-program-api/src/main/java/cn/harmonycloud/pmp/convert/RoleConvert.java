package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.RoleAddDto;
import cn.harmonycloud.pmp.model.dto.RoleDto;
import cn.harmonycloud.pmp.model.dto.RoleUpdateDto;
import cn.harmonycloud.pmp.model.entity.CheckRole;
import cn.harmonycloud.pmp.model.entity.Role;
import cn.harmonycloud.pmp.model.entity.RoleBase;
import cn.harmonycloud.pmp.model.vo.RoleBaseVo;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface RoleConvert {

    RoleConvert INSTANCE = Mappers.getMapper(RoleConvert.class);
    List<RoleBase> dos2Vos(List<Role> roles);
    Role dto2Role(RoleDto roleDto);
    RoleBaseVo do2Vo(Role role);
    Role dto2Do(RoleAddDto roleAddDto);
    Role dto2Do(RoleUpdateDto roleUpdateDto);
    CheckRole dto2Dto(RoleAddDto roleAddDto);
    CheckRole dto2Dto(RoleUpdateDto roleUpdateDto);
    RoleVo role2Vo(Role role);
    List<RoleVo> listDoToVo(List<Role> roles);
    List<RoleBase> dosToBases(List<Role> roles);
    Page<RoleBaseVo> pageToPage(Page<Role> rolePage);
}
