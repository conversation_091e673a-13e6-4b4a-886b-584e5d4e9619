package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.entity.Attachment;
import cn.harmonycloud.pmp.model.vo.AttachmentVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-10-13
 */
@Mapper
public interface AttachmentConvert {
    AttachmentConvert INSTANCE = Mappers.getMapper(AttachmentConvert.class);

    List<AttachmentVo> do2vos(List<Attachment> attachmentList);

    AttachmentVo do2vo(Attachment attachment);
}
