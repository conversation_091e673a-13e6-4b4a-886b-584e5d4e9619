package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.AppDto;
import cn.harmonycloud.pmp.model.entity.App;
import cn.harmonycloud.pmp.model.vo.AppVo;
import cn.harmonycloud.pmp.model.vo.GatewayAppVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface AppConvert {

    AppConvert INSTANCE = Mappers.getMapper(AppConvert.class);
    App dto2App(AppDto appDto);
    AppVo app2Vo(App app);
    GatewayAppVo do2GatewayVo(App app);
    List<AppVo> dos2Vos(List<App> apps);
}
