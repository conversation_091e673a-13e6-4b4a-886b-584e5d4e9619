package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.entity.UserOrganization;
import cn.harmonycloud.pmp.model.vo.UserOrganizationVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 项目包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface UserOrganizationConvert {

    UserOrganizationConvert INSTANCE = Mappers.getMapper(UserOrganizationConvert.class);
    UserOrganizationVo userOgran2Vo(UserOrganization userOrganization);
    List<UserOrganizationVo> dos2Vos(List<UserOrganization> userOrganizations);
}
