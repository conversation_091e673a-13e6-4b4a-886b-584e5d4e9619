package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.OrganizationAddDto;
import cn.harmonycloud.pmp.model.dto.OrganizationDto;
import cn.harmonycloud.pmp.model.dto.OrganizationForceControlDto;
import cn.harmonycloud.pmp.model.dto.OrganizationUpdateDto;
import cn.harmonycloud.pmp.model.entity.OrganUserBase;
import cn.harmonycloud.pmp.model.entity.Organization;
import cn.harmonycloud.pmp.model.entity.OrganizationTree;
import cn.harmonycloud.pmp.model.vo.OrganByUserVo;
import cn.harmonycloud.pmp.model.vo.OrganizationPageVo;
import cn.harmonycloud.pmp.model.vo.OrganizationTreeVo;
import cn.harmonycloud.pmp.model.vo.OrganizationVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;


@Mapper
public interface OrganizationConvert {
    OrganizationConvert INSTANCE = Mappers.getMapper(OrganizationConvert.class);

    Organization dto2Organ(OrganizationDto organizationDto);
    OrganizationDto dto2Dto(OrganizationAddDto organizationAddDto);
    OrganizationDto dto2Dto(OrganizationUpdateDto organizationUpdateDto);
    OrganizationVo organ2Vo(Organization Organization);
    Organization dto2do(OrganizationForceControlDto organizationForceControlDto);
    List<OrganizationVo> dosVos(List<Organization> organizations);
    List<OrganizationPageVo> dos2PageVos(List<Organization> organizations);
    List<OrganizationTree> dos2Trees(List<Organization> organizations);
    List<OrganizationTreeVo> dos2TreeVos(List<Organization> organizations);
    List<OrganizationTreeVo> dos2TreeVos(Set<Organization> organizations);
    List<OrganUserBase> dos2BaseVos(List<Organization> organizations);
    List<OrganByUserVo> dos2Vos(List<Organization> organizations);

}