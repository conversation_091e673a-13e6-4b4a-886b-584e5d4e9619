package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.TagByNameDto;
import cn.harmonycloud.pmp.model.entity.Tag;
import cn.harmonycloud.pmp.model.entity.TagAddDto;
import cn.harmonycloud.pmp.model.entity.TagTreeNode;
import cn.harmonycloud.pmp.model.vo.TagVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface TagConvert {

    TagConvert INSTANCE = Mappers.getMapper(TagConvert.class);
    List<TagTreeNode> tag2Node(List<Tag> tags);
    Tag dto2Do(TagAddDto tagAddDto);
    TagAddDto dto2Dto(TagByNameDto tagByNameDto);
    TagVo do2Vo(Tag tag);
    List<TagVo> dos2Vos(List<Tag> tags);
}
