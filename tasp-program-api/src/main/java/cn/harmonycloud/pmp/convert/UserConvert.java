package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.OrganUserRegisterDto;
import cn.harmonycloud.pmp.model.dto.UserRegisterDto;
import cn.harmonycloud.pmp.model.dto.UserSyncDTO;
import cn.harmonycloud.pmp.model.dto.UserUpdateDto;
import cn.harmonycloud.pmp.model.entity.OrganUserBase;
import cn.harmonycloud.pmp.model.entity.ResourceUser;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.CopResourceUserVo;
import cn.harmonycloud.pmp.model.vo.UserBaseVO;
import cn.harmonycloud.pmp.model.vo.UserInfoVo;
import cn.harmonycloud.pmp.model.vo.UserVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);
    UserVo doToVo(User user);
    User dtoToDo(UserRegisterDto registerDTO);
    User dtoToDo(OrganUserRegisterDto registerDTO);
    List<UserVo> dosToVos(List<User> users);
    List<ResourceUser> dosToBaseVos(List<User> users);
    User dtoToDo(UserUpdateDto userDTO);
    UserInfoVo doToInfoVo(User user);
    List<CopResourceUserVo> dos2Vos(List<User> users);
    List<OrganUserBase> dos2Bases(List<User> users);
    List<UserBaseVO> dos2Base(List<User> users);

    UserRegisterDto syncToRegisterDto(UserSyncDTO userSyncDTO);

    UserUpdateDto syncToUpdateDto(UserSyncDTO userSyncDTO);
}
