package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.AdministrativeDTO;
import cn.harmonycloud.pmp.model.dto.AdministrativeTreeDTO;
import cn.harmonycloud.pmp.model.entity.Administrative;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AdministrativeConvert {

    AdministrativeTreeDTO toTreeDTO(Administrative node);

    AdministrativeDTO toAdministrativeDTO(Administrative administrative);
}
