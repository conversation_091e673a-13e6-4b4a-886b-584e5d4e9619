package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.vo.ResourceInstanceVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色包装类转换接口
 * <AUTHOR>
 * @since 2021-09-16
 */
@Mapper
public interface ResourceInstanceConvert {

    ResourceInstanceConvert INSTANCE = Mappers.getMapper(ResourceInstanceConvert.class);
    List<ResourceInstanceVo> dos2Vos(List<ResourceInstance> resourceInstances);
    Page<ResourceInstanceVo> page2VoPage(Page<ResourceInstance> resourceInstancePage);
}
