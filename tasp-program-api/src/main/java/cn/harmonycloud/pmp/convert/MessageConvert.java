package cn.harmonycloud.pmp.convert;

import cn.harmonycloud.pmp.model.dto.MessageDTO;
import cn.harmonycloud.pmp.model.entity.MessageText;
import cn.harmonycloud.pmp.model.vo.MessageVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/12/2
 */
@Mapper
public interface MessageConvert {
    MessageConvert INSTANCE = Mappers.getMapper(MessageConvert.class);
    Page<MessageVO> doPageToVoPage(Page<MessageText> messageTextPage);
    MessageVO doToVo(MessageText messageText);
    MessageText dtoToDo(MessageDTO messageDTO);
}
