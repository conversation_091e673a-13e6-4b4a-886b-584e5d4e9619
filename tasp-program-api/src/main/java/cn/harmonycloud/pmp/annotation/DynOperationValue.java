package cn.harmonycloud.pmp.annotation;

import cn.harmonycloud.pmp.constant.DictCons.KeyType;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * 动态替换注解
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Component
public @interface DynOperationValue {

    /*
     * 获取动态key
     */
    @ApiModelProperty(value = "获取动态key")
    String key() default "";

    /*
     * 动态key位置
     */
    @ApiModelProperty(value = "动态key位置")
    KeyType keyType() default KeyType.request;

    /*
     * 获取动态值 jstl表达式
     */
    @ApiModelProperty(value = "获取动态值")
    String jstl() default "";


}
