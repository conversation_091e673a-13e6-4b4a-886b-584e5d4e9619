package cn.harmonycloud.pmp.annotation;//package cn.harmonycloud.pmp.annotation;
//
//import cn.harmonycloud.pmp.model.param.ExtendOperationAuditParam;
//
///**
// * 扩展操作审计记录参数
// */
//public abstract class AbstractOperationalAuditExtend implements OperationalAuditExtendConfigurer {
//
//
//    /**
//     * 获取所有扩展参数的聚合
//     * @return
//     */
//    public ExtendOperationAuditParam AggregationExtendOperationAuditParam(){
//        return  ExtendOperationAuditParam.builder()
//                .extendParam(ExtendOperationCustomParam())
//                .build();
//    };
//}
