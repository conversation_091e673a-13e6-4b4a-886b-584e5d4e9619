package cn.harmonycloud.pmp.annotation;

import cn.harmonycloud.pmp.constant.DictCons.OperationIdType;
import cn.harmonycloud.pmp.constant.DictCons.OperationLevel;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * 平台操作记录
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Component
public @interface OperationAudit {

    /*
     * 操作描述
     */
    String operationName() default "";

    /*
     * 审计实体编码
     */
    String operationCode() default "";

    /*
     * 获取动态值 jstl表达式
     */
    DynOperationValue[] dynOperationValues() default {};

    /*
     * 审计实体主键值
     */
    String operationId() default "";

    /**
     * 审计实体主键值位置
     * @return
     */
    OperationIdType operationIdType() default OperationIdType.request;

    /*
     * 请求参数记录开关
     */
    boolean requestFlag() default true;

    /*
     * 返回参数记录开关
     */
    boolean responseFlag() default true;

    /*
     * 记录额外信息,暂时只支持从request中拿数据
     */
    String extendParam() default "";

    /*
     * 记录额外信息,暂时只支持从request中拿数据
     */
    OperationLevel operationLevel() default OperationLevel.critical;


}
