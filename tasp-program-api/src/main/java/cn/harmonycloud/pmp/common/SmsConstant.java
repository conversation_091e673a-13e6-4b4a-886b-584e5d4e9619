package cn.harmonycloud.pmp.common;

/**
 * 短信三方接口常量定义
 * <AUTHOR>
 * @since 2020-11-23
 */
public interface SmsConstant {
    String ACCESS_TOKEN_KEY = "accessToken";
    /**
     * access_token  prefix
     */
    String TOKEN_PREFIX = "Bearer ";

    /**
     * 获取AccessToken接口地址
     */
    String ACCESS_TOKEN_URL = "http://openapi.saicmotor.com/api/am/store/v0.12/applications/getAccessToken?Authorize=Basic%20eHJ5eThvdTYyMDo5NDRmOTU4Ny04YzRiLTRhN2QtODk3Mi0xOWJhODM3MDAyZTg=";

    /**
     * 发送短信接口地址
     */
    String SEND_SMS_URL_FORMAT = "https://openapi.saicmotor.com/opensaic/cloud/sms/v1.0.0/msg/%s";

    String SMS_CHECK_BODY = "{\"content\":\"SRC短信通知： \n这是一个短信测试信息，请勿回复！\",\"dest_id\":\"18311270951\",\"market\":false,\"signature_id\":0,\"template_id\":0,\"parameters\":[],\"type\":3}";

    String AUTH = "Authorization";

    String SEED = "X-SMS-SEED";

    String SIGN = "X-SMS-SIGN";

    /**
     *创建模板接口地址
     */
    String CREATE_SMS_TEMPLATE_URL = "https://openapi.saicmotor.com/opensaic/cloud/sms/v1.0.0/organization/%s/template";

    /**
     * 更新模板接口地址
     */
    String UPDATE_SMS_TEMPLATE_URL = "https://openapi.saicmotor.com/opensaic/cloud/sms/v1.0.0/organization/%s/template/%s";

    /**
     * 获取短信状态接口地址
     */
    String GET_SMS_STATUS_URL = "https://openapi.saicmotor.com/opensaic/cloud/sms/v1.0.0/msg/%s/%s";

}
