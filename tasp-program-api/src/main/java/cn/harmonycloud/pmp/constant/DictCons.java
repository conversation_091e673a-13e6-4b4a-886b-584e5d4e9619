package cn.harmonycloud.pmp.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
public interface DictCons {

    @Getter
    @AllArgsConstructor
    enum OperationAuditArchive{
        YES("归档",0),
        NO("不归档",1)
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum IpForcedControl{
        YES("是",true),
        NO("关",false)
        ;
        private final String name;
        private final Boolean value;
    }

    @Getter
    @AllArgsConstructor
    enum EventType{
        PROJECT_VIEW("项目浏览埋点","project_view")
        ;
        private final String name;
        private final String value;
    }


    @Getter
    @AllArgsConstructor
    enum WhiteIpFlag{
        YES("是",true),
        NO("关",false)
        ;
        private final String name;
        private final Boolean value;
    }


    @Getter
    @AllArgsConstructor
    enum OrganUserType{
        Organ("租户",1),
        User("用户",2)
        ;
        private final String name;
        private final Integer value;
    }


    @Getter
    @AllArgsConstructor
    enum OperationAuditConfigType{
        COMMON("通用类型",0),
        OperationCode("操作事项",1)
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum Status {
        NORMAL("正常", 0),
        BANED("锁定", 1),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum AppType {
        BASIC_PLAT("基座平台", 0),
        FRONT("前台", 1),
        BACK("后台", 2),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum LeftShow {
        Show("左侧展示", 1),
        Hidden("左侧不展示", 0),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum OrganFlag {
        Single("单租户模式", 1),
        MutiOne("多租户模式下单租户", 2),
        MutiMore("多租户模式下多租户", 3),
        ;
        private final String name;
        private final Integer value;
    }


    @Getter
    @AllArgsConstructor
    enum UserWideType {
        DingTalk("钉钉", 1),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum TagScope {
        Plat("平台及以下", 1),
        Organ("租户及以下", 2),
        Resource("资源", 3),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum OperationIdType {
        request("request"),
        response("response");
        String code;
    }


    @Getter
    @AllArgsConstructor
    enum TagClassificationCode {
        Organ("organ","租户标签分类"),
        ;
        String code;
        String name;
    }

    @Getter
    @AllArgsConstructor
    enum OperationLevel {
        info("info",1),
        critical("critical",2),
        ;
        String code;
        Integer value;

        public static Integer getValueByCode(String code) {
            return Arrays.stream(values())
                    .filter(OperationLevel -> OperationLevel.getCode().equals(code))
                    .map(OperationLevel::getValue)
                    .findFirst()
                    .get();
        }
    }

    @Getter
    @AllArgsConstructor
    enum KeyType {
        request("request"),
        response("response");
        String code;
    }

    @Getter
    @AllArgsConstructor
    enum IsAdmin {
        NO("否", 0),
        YES("是", 1),
        ;
        private final String name;
        private final Integer value;
    }


    @Getter
    @AllArgsConstructor
    enum CountType {
        Organ("用户喜好租户", 1),
        ;
        private final String name;
        private final Integer value;
    }


    @Getter
    @AllArgsConstructor
    enum AppCode {
        AMP("应用管理平台", "application"),
        SkyView("观云台", "skyview"),
        Devops("项目管理系统", "devops"),
        ;
        private final String name;
        private final String code;
    }

    @Getter
    @AllArgsConstructor
    enum Checked {
        Checked("选中", true),
        NotChecked("未选中", false),
        ;
        private final String name;
        private final Boolean value;
    }

    /**
     * 权限可见性
     */
    @Getter
    @AllArgsConstructor
    enum PermissionVisible {
        VISIBLE("可见", true),
        NOT_VISIBLE("不可见", false),
        ;
        private final String name;
        private final Boolean value;
    }


    @Getter
    @AllArgsConstructor
    enum Role {
        Admin(1L,"admin", "admin"),
        BaseRole(2L,"初始用户角色", "baseRole"),
        OrganManager(3L,"租户管理者", "organManager"),
        ResourceManager(4L,"管理员", "resourceManager"),
        OrganNormal(5L, "租户普通用户", "organNormal"),
        ResourceNormal(6L, "普通成员", "resourceNormal"),
        ProjectAdminRole(18L,"项目管理员", "projectAdminRole"),
        ProjectNormalRole(19L,"项目成员", "projectNormalRole"),
        PipelineAdminRole(20L,"管理员", "pipeline_admin_role"),
        PipelineExecuteRole(21L,"可执行", "pipeline_execute_role"),
        PipelineViewRole(22L,"可查看", "pipeline_view_role"),
        CredentialAdminRole(23L,"管理员", "credential_admin_role"),
        CredentialNormalRole(24L,"成员", "credential_normal_role"),
        DeployEnvAdminRole(25L,"管理员", "deploy_env_admin_role"),
        DeployEnvNormalRole(26L,"成员", "deploy_env_normal_role"),
        DeploySqlAdmin(34L,"管理员", "deploy_sql_admin"),
        DeploySqlNormal(35L,"使用者", "deploy_sql_normal"),


        ;
        private final Long id;
        private final String name;
        private final String code;
    }


    @Getter
    @AllArgsConstructor
    enum RoleType {
        Plat(1, "平台角色"),
        OrganShare(2, "租户共享角色"),
        OverAll(3, "全局角色"),
        ResourceType(4, "资源类型角色"),
        Organ(5, "租户角色"),
        ResourcePrivate(6, "资源实例私有角色"),
        ;
        private final Integer value;
        private final String name;

       public static String getNameByValue(Integer value){
           return Arrays.stream(values())
               .filter(roleType -> roleType.getValue().equals(value))
               .map(RoleType::getName)
               .findFirst()
               .get();
        }
    }

    /**
     * sys_role
     * 系统角色类型
     */
    @Getter
    @AllArgsConstructor
    enum RoleTypeCode {
        PLAT("plat", "内置角色"),
        ORGAN("organ", "内置角色"),
        DEFAULT_RESOURCE("default-resource", "内置角色"),
        DEVOPS_SYSTEM("devops-system", "资源-系统内置角色"),
        DEVOPS_SUBSYSTEM("devops-subsystem", "资源-系统内置角色"),
        DEVOPS_GITLAB("devops-gitlab", "资源-代码仓库内置角色"),
        DEVOPS_GROUP("devops-group", "资源-代码仓库租户内置角色"),
        PROJECT("project", "资源-项目内置角色");

        /**
         * 角色类型编码
         */
        private String roleTypeCode;
        /**
         * 描述
         */
        private String desc;
    }


    @Getter
    @AllArgsConstructor
    enum ResourceOwnerType {
        User("1","人员"),
        Role("2","角色"),
        UserGroup("3","用户组"),
        ;
        private final String value;
        private final String name;
    }

    @Getter
    @AllArgsConstructor
    enum UserType {
        Inner(1,"内部用户"),
        Outter(2,"外部用户"),
        ;
        private final Integer value;
        private final String name;
        public static Integer getVale(String name){
            return Arrays.stream(values())
                    .filter(userType -> userType.getName().equals(name))
                    .map(UserType::getValue)
                    .findFirst()
                    .get();
        }
    }


    @Getter
    @AllArgsConstructor
    enum Gender {
        WOMAN("女", 0),
        MAN("男", 1),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum RouteResult {
        SUCCEED("成功", 0),
        FAILED("失败", 1),
        ;
        private final String name;
        private final Integer value;
    }



    @Getter
    @AllArgsConstructor
    enum PermissionKind {
        PLATFORM_PERMISSION("平台菜单", 1),
        ORGANIZATION_PERMISSION("租户菜单", 2),
        RESOURCE_PERMISSION("资源菜单", 3),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum AppNeeded {
        APP_NEEDED("顶层包含应用层", -1l),
        APP_NOT_NEEDED("顶层不包含应用层", 0l),
        ;
        private final String name;
        private final long value;
    }

    @Getter
    @AllArgsConstructor
    enum PermissionType {
        PERMISSION("菜单", 1),
        BUTTON("功能点", 2),
        TAB("tab页", 3),
        ;
        private final String name;
        private final Integer value;

        public static List<Integer> getvalues(){
            return Arrays.stream(values()).map(PermissionType::getValue).collect(Collectors.toList());
        }
    }

    @Getter
    @AllArgsConstructor
    enum  DepartIsRoot{
        Root("根节点", 1),
        NotRoot("非根节点", 0),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum RoleEditable {
        EDITABLE("可以修改", true),
        NOT_EDITABLE("不可修改", false),
        ;
        private final String name;
        private final Boolean value;
    }

    @Getter
    @AllArgsConstructor
    enum FirstLogin {
        YES("是", 1),
        NO("否", 0),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum PasswordExpired {
        FAR_EXPIRE("密码还没过期", 1),
        WILL_EXPIRE("密码即将过期", 2),
        IS_EXPIRED("密码已过期", 3),
        FIRST_LOGIN("首次登录", 4),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum ResourceType {
        Default("默认实现", "default"),
        ProjectScrum("项目-敏捷项目管理", "project_scrum"),
        ProjectWaterfall("项目-瀑布项目规划", "project_waterfall"),
        ProjectCommon("项目-通用任务管理", "project_common"),
        ProjectDemand("项目-需求项目管理", "project_demand"),
        Project("项目", "project"),
        System("系统管理", "system"),
        SubSystem("子系统管理", "subsystem"),
        Gitlab("代码仓库", "gitlab"),
        Pipeline("流水线", "pipeline"),
        Credential("凭据", "credential"),
        DeployEnv("环境", "deployEnv"),
        ;
        private final String name;
        private final String value;
    }

    @Getter
    @AllArgsConstructor
    enum ProjectStatus {
        UN_START("未开始", "2"),
        IN_PROGRESS("进行中", "1"),
        FINISHED("已完成", "3"),
        ARCHIVED("已归档", "4"),
        ;
        private final String name;
        private final String value;
    }


    @Getter
    @AllArgsConstructor
    enum Trans2Code {
        Project_SCRUM("project", "project_scrum","项目"),
        Project_WATERFALL("project", "project_waterfall","项目"),
        Project_COMMON("project", "project_common","项目"),
        Project_DEMAND("project", "project_demand","项目"),
        ;
        private final String parentCode;
        private final String code;
        private final String parentName;

        public static String getByCode(String code) {
            String resulCode = code;
            for (Trans2Code trans2Code : values()) {
                if (trans2Code.getCode().equals(code)) {
                    resulCode = trans2Code.getParentCode();
                    break;
                }
            }
            return resulCode;
        }
        public static String getParentNameByCode(String code) {
            String resultName = "";
            for (Trans2Code trans2Code : values()) {
                if (trans2Code.getCode().equals(code)) {
                    resultName = trans2Code.getParentName();
                    break;
                }
            }
            return resultName;
        }
    }

    @Getter
    @AllArgsConstructor
    enum  TagTagClass{
        TAG_CLASS("标签分类", 1),
        TAG("标签", 2),
        ;
        private final String name;
        private final Integer value;
    }

    @Getter
    @AllArgsConstructor
    enum  sendAll{
        No("否",0 ),
        Yes("是", 1),
        ;
        private final String name;
        private final Integer value;
    }
}
