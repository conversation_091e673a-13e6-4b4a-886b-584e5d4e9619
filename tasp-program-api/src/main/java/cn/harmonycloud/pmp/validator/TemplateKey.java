package cn.harmonycloud.pmp.validator;

import jakarta.validation.Constraint;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR> working on 2021/4/9 11:04 上午
 * @Description
 * @Version 1.0
 */
@Documented
@Constraint(validatedBy = TemplateKeyValidator.class)
@Target({PARAMETER, FIELD})
@Retention(RUNTIME)
public @interface TemplateKey {
    String message() default "模板键值格式：字母开头，由字母、下划线、数字组成，不超过100个字";

    Class[] groups() default {};

    Class[] payload() default {};
}
