package cn.harmonycloud.pmp.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR> working on 2021/4/9 11:04 上午
 * @Description
 * @Version 1.0
 */
public class TemplateKeyValidator implements ConstraintValidator<TemplateKey, String> {
    @Override
    public boolean isValid(String field, ConstraintValidatorContext context) {
        if (field == null) {
            // cannot be null
            return true;
        }
        // 模板键值格式：字母开头，由字母、下划线、数字组成，不超过100个字
        String regExp = "^[a-zA-Z]([a-zA-Z0-9_]{1,100})";
        return field.matches(regExp);
    }
}
