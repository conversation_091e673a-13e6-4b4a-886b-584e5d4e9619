package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 部门表
 *
 * <AUTHOR>
 * @date 2021-12-01 11:01:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_department")
@ApiModel(value = "Department对象", description = "部门表")
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "父级ID")
    @TableField("parent_id")
    private Long parentId;
     /**
     * 租户ID
     */
    @ApiModelProperty(name = "租户ID")
    @TableField(value ="organization_id")
    @NotNull(message = "租户不能为空")
    private Long organizationId;

     /**
     * 名称
     */
    @ApiModelProperty(name = "名称")
    @TableField(value ="dept_name")
    @NotBlank(message = "部门名称不能为空")
    private String name;

    /**
     * 唯一编码
     */
    @ApiModelProperty(name = "唯一编码")
    @TableField(value ="unique_code")
    private String uniCode;

    /**
     * 排序号
     */
    @ApiModelProperty(name = "是否是根部门 0-否 1-是")
    @TableField(value ="is_root")
    private Integer isRoot;

     /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    @TableField(value ="dept_description")
    private String description;

     /**
     * 排序号
     */
    @ApiModelProperty(name = "排序号")
    @TableField(value ="sort_id")
    private Integer sortId;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

     /**
     * 乐观锁
     */
    @ApiModelProperty(name = "乐观锁")
    @TableField(value ="version")
    @JsonIgnore
    @Version
    private Integer version;

     /**
     * 逻辑删除 0正常 1删除
     */
    @ApiModelProperty(name = "逻辑删除 0正常 1删除")
    @TableField(value ="is_deleted")
    @TableLogic
    @JsonIgnore
    private Integer isDeleted;

}
