package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceInstanceParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class UserResourcePermissionDto implements Serializable {

    @ApiModelProperty(value = "父类code")
    String  parentCode;

    @ApiModelProperty(value = "资源实例集合")
    List<ResourceInstanceParam> resourceInstanceParamList;
}
