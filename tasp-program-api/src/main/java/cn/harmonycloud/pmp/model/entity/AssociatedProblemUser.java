package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 钉钉关联异常用户表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "钉钉关联异常对象", description = "钉钉关联异常用户表")
@TableName("sys_associated_problem_user")
public class AssociatedProblemUser {
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("UNIQUE_ID")
    private String uniqueId;

    /**
     * 异常类型 0-姓名重复 1-其他
     */
    @ApiModelProperty(value = "异常类型")
    @TableField("EXCEPTION_TYPE")
    private Integer exceptionType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
}