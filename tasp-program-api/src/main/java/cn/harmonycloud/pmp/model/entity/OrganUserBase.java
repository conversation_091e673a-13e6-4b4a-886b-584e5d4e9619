package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="资源下查询租户用户对象", description="资源下查询租户用户对象参数")
public class OrganUserBase implements Serializable {

    @ApiModelProperty(value = "节点id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型 1-租户 2-人员")
    private Integer type;

    public OrganUserBase(Long id, String name) {
        this.id = id;
        this.name = name;
    }
}
