package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息发件箱
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("notice_message_outbox")
@ApiModel(value = "MessageOutbox对象", description = "消息发件箱")
public class MessageOutbox implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "发送者ID")
    @TableField("send_id")
    private Long sendId;

    @ApiModelProperty(value = "收件人类型")
    @TableField("receiver_type")
    private Integer receiverType;

    @ApiModelProperty(value = "接受者ID，0表示接受者为所有人")
    @TableField("receive_id")
    private Long receiveId;

    @ApiModelProperty(value = "消息ID")
    @TableField("text_id")
    private Long textId;

    @ApiModelProperty(value = "是否发布")
    @TableField("is_published")
    private Boolean published;

    @ApiModelProperty(value = "发布时间")
    @TableField(value = "publish_time")
    private LocalDateTime publishTime;


    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
