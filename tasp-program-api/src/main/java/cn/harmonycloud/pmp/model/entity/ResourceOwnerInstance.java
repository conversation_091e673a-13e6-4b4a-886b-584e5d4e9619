package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 资源拥有者实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_resource_owner_instance")
@ApiModel(value="ResourceOwnerInstance对象", description="资源拥有者实例表")
public class ResourceOwnerInstance implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "字典表的resource_owner的下属value")
    @TableField("type_dict_value")
    private String typeDictValue;

    @ApiModelProperty(value = "应用资源拥有者id")
    @TableField("resource_owner_instance_id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "子应用资源拥有者名称")
    @TableField("resource_owner_instance_name")
    private String resourceOwnerInstanceName;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "资源拥有者状态")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "排序号")
    @TableField("roi_sort")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    @TableField("roi_description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("extend_field")
    private String extendField;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean bolDeleted;

    /**
     * <p>
     * 用户角色关联表
     * </p>
     *
     * <AUTHOR>
     * @since 2021-09-14
     */
    @Data
    public static class UserRolesByResourceUser implements Serializable {

        @ApiModelProperty(value = "用户ID")
        private Long userId;

        @ApiModelProperty(value = "用户ID")
        private List<Long> userIds;

        @ApiModelProperty(value = "租户id",hidden = true)
        private Long organId;

        @ApiModelProperty(value = "资源类型")
        private String resourceTypeCode;

        @ApiModelProperty(value = "应用资源id")
        private Long resourceInstanceId;

    }
}
