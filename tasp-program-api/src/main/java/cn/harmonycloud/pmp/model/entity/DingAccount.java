package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@SuperBuilder
@TableName("hms_ding_account")
public class DingAccount {

    public DingAccount() {
    }

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "钉钉类型")
    @TableField(value = "type")
    private int type;

    @ApiModelProperty(value = "公司名称")
    @TableField(value = "company_name")
    private String companyName;

    @ApiModelProperty(value = "备注")
    @TableField(value = "annotation")
    private String annotation;

    @ApiModelProperty(value = "是否开启第三方登录")
    @TableField(value = "allow_login")
    private Boolean allowLogin;

    @ApiModelProperty(value = "是否开启消息通知")
    @TableField(value = "allow_message_notice")
    private Boolean allowMessageNotice;

    @ApiModelProperty(value = "专有钉域名")
    @TableField(value = "domain")
    private String domain;

    @ApiModelProperty(value = "逻辑删除")
    @TableField(value = "del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
