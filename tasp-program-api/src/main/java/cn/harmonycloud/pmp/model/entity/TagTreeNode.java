package cn.harmonycloud.pmp.model.entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="TagTree复合对象", description="TagTree复合对象")
public class TagTreeNode implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "标签/标签分类名")
    private String name;

    @ApiModelProperty(value = "类型：1 标签分类 2 标签")
    private Integer type;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "应用ID")
    private Long organId;

    @ApiModelProperty(value = "资源类型编码")
    private String typeCode;

    @ApiModelProperty(value = "资源实例id")
    private String instanceId;
}
