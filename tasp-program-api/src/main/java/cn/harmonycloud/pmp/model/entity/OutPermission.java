package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class OutPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "父级ID")
    private String parentId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "类型 1.菜单 2.按钮 3.tab页")
    private Integer type;

    @ApiModelProperty(value = "种类 1.平台菜单 2.租户菜单 3.资源菜单")
    private Integer kind;

    @ApiModelProperty(value = "应用ID")
    private String appId;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "请求方式 1.GET 2.POST 3.PUT 4.DELETE")
    private Integer method;

    @ApiModelProperty(value = "是否可见")
    private Boolean visible;

    @ApiModelProperty(value = "接口列表")
    private String url;

    @ApiModelProperty(value = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "资源类型编码")
    private String resourceTypeCode;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    private Boolean deleted;

    @ApiModelProperty(value = "是否是iframe")
    private Boolean isIframe;

    @ApiModelProperty(value = "iframe url")
    private String iframeUrl;

}
