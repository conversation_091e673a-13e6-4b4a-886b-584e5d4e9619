package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class MapByResources implements Serializable {

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例id")
    private List<Long> resourceInstanceIds;

    @ApiModelProperty(value = "用户id",hidden = true)
    private Long userId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;
}
