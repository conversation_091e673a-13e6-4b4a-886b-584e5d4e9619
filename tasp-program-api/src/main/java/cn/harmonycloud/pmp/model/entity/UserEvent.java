package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_event")
@ApiModel(value="UserEvent对象", description="埋点表")
public class UserEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "租户id")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "埋点事件类型")
    @TableField("event_type")
    private String eventType;

    @ApiModelProperty(value = "埋点事件")
    @TableField(value = "event_data")
    private String eventData;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
