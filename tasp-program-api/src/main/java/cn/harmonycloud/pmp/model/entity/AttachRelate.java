package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 附件
 *
 * <AUTHOR>
 * @date 2021-08-26 14:04:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("attach_relate")
@ApiModel(value = "attach_relate", description = "附件关联表")
public class AttachRelate implements Serializable {

  @TableId(value = "id", type = IdType.ASSIGN_ID)
  @ApiModelProperty("id")
  private Long id;

  @ApiModelProperty(name = "关联对象id")
  @TableField(value = "relate_id")
  private Long relateId;

  @ApiModelProperty(name = "类型 1-应用 2-菜单")
  @TableField(value = "type")
  private String type;

  @ApiModelProperty(value = "创建人")
  @TableField(value = "create_by", fill = FieldFill.INSERT)
  private String createBy;

  @ApiModelProperty(value = "创建时间")
  @TableField(value = "create_time", fill = FieldFill.INSERT)
  private LocalDateTime createTime;


}
