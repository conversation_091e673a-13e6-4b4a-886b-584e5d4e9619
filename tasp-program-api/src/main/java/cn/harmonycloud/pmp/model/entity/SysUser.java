package cn.harmonycloud.pmp.model.entity;

import cn.harmonycloud.pmp.model.vo.UserInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户表
 */
@Data
@ApiModel(value = "用户")
public class SysUser {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "名称")
    private String nickname;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "email")
    private String email;

    public SysUser(){

    }

    public SysUser(User user){
        this.id=user.getId();
        this.username=user.getUsername();
        this.nickname=user.getName();
        this.password=user.getPassword();
        this.email=user.getEmail();
    }

    public SysUser(UserInfoVo userInfoVo){
        this.id=userInfoVo.getId();
        this.username=userInfoVo.getUsername();
        this.nickname=userInfoVo.getName();
        this.email=userInfoVo.getEmail();
    }
}
