package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * </p>
 * <AUTHOR>
 * @since 2022-08-02
 */
@Data
@ApiModel(value="OperationAuditPO对象", description="")
public class QueryOperationAuditListDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "应用编码",hidden = true)
    private String appCode;

    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    @ApiModelProperty(value = "审计实体主键值")
    private String operationId;

}
