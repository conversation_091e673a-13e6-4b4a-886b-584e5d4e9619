package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.InputStream;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 附件
 *
 * <AUTHOR>
 * @date 2021-08-26 14:04:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("attachment")
@ApiModel(value = "Attachment", description = "附件")
public class Attachment implements Serializable {

  /**
   * id
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  @ApiModelProperty("id")
  private Long id;

  /**
   * minio的存储空间名称
   */
  @ApiModelProperty(name = "minio的存储空间名称")
  @TableField(value = "bucket_name")
  private String bucketName;

  /**
   * 附件名称
   */
  @ApiModelProperty(name = "附件名称")
  @TableField(value = "name")
  private String name;

  /** 大小,单位:kb */
  @ApiModelProperty(name = "大小,单位:kb")
  @TableField(value = "size")
  private String size;

  /**
   * 附件地址
   */
  @ApiModelProperty(name = "附件地址")
  @TableField(value = "url")
  private String url;

  @ApiModelProperty(name = "二进制图片流")
  @TableField(value = "image_flow")
  private InputStream imageFlow;

  @ApiModelProperty(value = "创建人")
  @TableField(value = "create_by", fill = FieldFill.INSERT)
  private String createBy;

  @ApiModelProperty(value = "创建时间")
  @TableField(value = "create_time", fill = FieldFill.INSERT)
  private LocalDateTime createTime;

  @ApiModelProperty(value = "更新人")
  @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
  private String updateBy;

  @ApiModelProperty(value = "更新时间")
  @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "逻辑删除 0正常 1删除")
  @TableField("is_deleted")
  @TableLogic
  private Boolean deleted;

}
