package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * </p>
 * <AUTHOR>
 * @since 2022-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_operation_audit")
@ApiModel(value="OperationAuditPO对象", description="")
public class OperationAuditPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    @TableField("trace_id")
    @ApiModelProperty(value = "跟踪编号")
    private String traceId;

    @TableField("organ_id")
    @ApiModelProperty(value = "租户id")
    private Long organId;

    @TableField("app_code")
    @ApiModelProperty(value = "应用编码")
    private String appCode;

    @TableField("app_name")
    @ApiModelProperty(value = "应用名称")
    private String appName;

    @TableField("operation_code")
    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    @TableField("operation_name")
    @ApiModelProperty(value = "操作名称")
    private String operationName;

    @TableField("attrib_operation")
    @ApiModelProperty(value = "属性操作")
    private String attribOperation;

    @TableField("operation_id")
    @ApiModelProperty(value = "审计实体主键值")
    private String operationId;

    @TableField("ip_address")
    @ApiModelProperty(value = "ip")
    private String ipAddress;

    @TableField("operation_start_time")
    @ApiModelProperty(value = "操作开始时间")
    private LocalDateTime operationStartTime;

    @TableField("operation_end_time")
    @ApiModelProperty(value = "操作结束时间")
    private LocalDateTime operationEndTime;

    @TableField("operation_user_id")
    @ApiModelProperty(value = "操作用户ID")
    private String operationUserId;

    @TableField("param")
    @ApiModelProperty(value = "参数")
    private String param;

    @TableField("request_type")
    @ApiModelProperty(value = "请求类型POST/GET")
    private String requestType;

    @TableField("request_url")
    @ApiModelProperty(value = "URL")
    private String requestUrl;

    @TableField("class_name")
    @ApiModelProperty(value = "类名")
    private String className;

    @TableField("method")
    @ApiModelProperty(value = "方法名")
    private String method;

    @TableField("response")
    @ApiModelProperty(value = "返回json")
    private String response;

    @TableField("response_code")
    @ApiModelProperty(value = "返回HTTP code")
    private String responseCode;

    @TableField("result")
    @ApiModelProperty(value = "操作结果")
    private String result;

    @TableField("extend_param")
    @ApiModelProperty(value = "自定义扩展字段")
    private String extendParam;

    @TableField("is_deleted")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;




}
