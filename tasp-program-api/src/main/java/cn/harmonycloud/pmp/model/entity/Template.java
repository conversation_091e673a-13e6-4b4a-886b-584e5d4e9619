package cn.harmonycloud.pmp.model.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: Template
 * @projectName src-message-svc
 * @date 2020/11/4 14:58
 */

@Data
@ToString(callSuper = true)
@ApiModel("消息模板表")
@TableName("hms_template")
public class Template {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    @TableField("tenant_id")
    private Long tenantId;

    @ApiModelProperty(value = "消息标题")
    @TableField("title")
    private String title;

    @ApiModelProperty(value = "模板名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "功能模块ID")
    @TableField(value = "category_id",updateStrategy = FieldStrategy.IGNORED)
    private Long categoryId;

    @ApiModelProperty(value = "功能模块")
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "触发事件ID")
    @TableField(value = "trigger_id",updateStrategy = FieldStrategy.IGNORED)
    private Long triggerId;

    @ApiModelProperty(value = "触发事件")
    @TableField(exist = false)
    private String triggerName;

    @ApiModelProperty(value = "发送类型")
    @TableField(value = "send_type",updateStrategy = FieldStrategy.IGNORED)
    private Integer sendType;

    @ApiModelProperty(value = "模板内容")
    @TableField("content")
    private String content;

    @ApiModelProperty(value = "接收组")
    @TableField("receive_group")
    private String receiveGroup;

    @ApiModelProperty(value = "发送给所有人 0-否 1-是")
    @TableField("send_all")
    private Integer sendAll;

    @ApiModelProperty(value = "模板内容键值",hidden = true)
    @TableField("content_keys")
    private String contentKeys;

    @ApiModelProperty(value = "备注信息")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty(value = "模板内容键值列表")
    @TableField(exist = false)
    private List<Map<String,String>> contentKeyList;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    public void setContentKeys(String contentKeys) {
        List<Map<String,String>> contentKeyList = JSON.parseObject(contentKeys,List.class);
        this.setContentKeyList(contentKeyList);
        this.contentKeys = contentKeys;
    }
}
