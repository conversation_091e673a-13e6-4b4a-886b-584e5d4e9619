package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 附件
 *
 * <AUTHOR>
 * @date 2021-08-26 14:04:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_file")
@ApiModel(value = "File", description = "文件")
public class AmpFile implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(name = "minio的存储空间名称")
    @TableField(value = "path")
    private String path;

    @ApiModelProperty(name = "附件名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(name = "存储系统的唯一键，方便做印射关系")
    @TableField(value = "unique_key")
    private String uniqueKey;

    @ApiModelProperty(name = "大小,单位:kb")
    @TableField(value = "size")
    private Long size;

    @ApiModelProperty(name = "版本号")
    @TableField(value = "version")
    private Integer version;

    @ApiModelProperty(name = "在线地址")
    @TableField(value = "link")
    private String link;


    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("del_flg")
    @TableLogic
    private Boolean delFlag;

}
