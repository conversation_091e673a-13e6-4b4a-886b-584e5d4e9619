package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询不同用户在同一资源下各自拥有的角色
 *
 * @Date 2023-02-15 16:35
 */
@Data
public class UserResourceRole {
    @ApiModelProperty(value = "资源主键ID集合")
    private List<Long> resourceIds;

    @ApiModelProperty(value = "资源主键ID")
    private Long resourceId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "角色ID集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "租户id")
    private Long organId;
}
