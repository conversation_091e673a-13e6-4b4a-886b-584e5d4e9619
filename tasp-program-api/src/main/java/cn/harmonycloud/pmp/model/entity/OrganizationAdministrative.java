package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 租户和行政组织关联关系表
 * <AUTHOR>
 * @Date 2024/7/8 11:56 上午
 **/
@Data
@TableName("sys_organization_administrative")
@ApiModel(value="租户行政组织关联关系表", description="租户行政组织关联关系表")
public class OrganizationAdministrative {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "行政组织")
    private Long adminId;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
