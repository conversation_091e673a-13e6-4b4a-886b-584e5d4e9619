package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class ResourceGroupNode implements Serializable {

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "业务主键ID")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父类实例id")
    private Long parentResourceInstanceId;

    @ApiModelProperty(value = "父类菜单code")
    private String parentPermissionCode;

    @ApiModelProperty(value = "跳转地址")
    private String redirectUrl;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    public ResourceGroupNode(Long organId,String resourceTypeCode, Long resourceInstanceId, String name,
                             Long parentResourceInstanceId, String parentPermissionCode, String redirectUrl) {
        this.organId = organId;
        this.resourceTypeCode = resourceTypeCode;
        this.resourceInstanceId = resourceInstanceId;
        this.name = name;
        this.parentResourceInstanceId = parentResourceInstanceId;
        this.parentPermissionCode = parentPermissionCode;
        this.redirectUrl = redirectUrl;
    }

    public ResourceGroupNode() {
    }
}
