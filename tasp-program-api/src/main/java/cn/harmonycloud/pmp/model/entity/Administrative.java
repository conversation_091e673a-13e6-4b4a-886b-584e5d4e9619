package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 行政组织实体
 * <AUTHOR>
 * @Date 2024/7/5 2:28 下午
 **/
@Data
@TableName("sys_administrative")
@ApiModel(value="行政组织信息表", description="行政组织信息表")
public class Administrative {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "行政组织名称")
    private String adminName;

    @ApiModelProperty(value = "行政组织编码")
    private String code;

    @ApiModelProperty(value = "父级行政组织id")
    private Long parentId;

    @ApiModelProperty(value = "路径")
    private String path;

    @ApiModelProperty(value = "根节点id")
    private Long rootId;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableLogic
    private Boolean delFlag;

    public static Administrative defaultRootNode() {
        Administrative root = new Administrative();
        root.setId(0L);
        root.setPath("");
        root.setRootId(null);
        return root;
    }
}
