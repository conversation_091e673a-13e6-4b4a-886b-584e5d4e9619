package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("system_config")
@ApiModel(value="系统配置", description="系统配置")
public class SystemConfig {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id")
    private Integer id;

    @ApiModelProperty(value = "配置名")
    @TableField("config_name")
    private String configName;

    @ApiModelProperty(value = "配置内容")
    @TableField("config_value")
    private String configValue;

    @ApiModelProperty(value = "配置类型")
    @TableField("config_type")
    private String configType;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "修改人")
    @TableField("update_user")
    private String updateUser;


    public static class Fields {
        public static final String configName = "config_name";
        public static final String configValue = "config_value";
        public static final String configType = "config_type";
        public static final String createUser = "create_user";
        public static final String updateUser = "update_user";
    }

}
