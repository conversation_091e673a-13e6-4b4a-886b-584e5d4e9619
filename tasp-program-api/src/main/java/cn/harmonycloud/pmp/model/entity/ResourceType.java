package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资源类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_resource_type")
@ApiModel(value="ResourceType对象", description="资源类型表")
public class ResourceType implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "应用id")
    @TableField("app_id")
    @NotNull(message = "应用id，不能为空!")
    private Long appId;

    @ApiModelProperty(value = "应用id")
    @TableField("parent_id")
    private Long parentId=0L;

    @ApiModelProperty(value = "父类名称")
    @TableField(exist = false)
    private String parentName;

    @ApiModelProperty(value = "包含类")
    @TableField("species")
    private Long species;

    @ApiModelProperty(value = "包含类名称")
    @TableField(exist = false)
    private String speciesName;

    @ApiModelProperty(value = "编码")
    @TableField("srt_code")
    @NotBlank(message = "资源类型编码不能为空！")
    private String code;

    @ApiModelProperty(value = "名称")
    @TableField("srt_name")
    @NotBlank(message = "资源类型名称不能为空！")
    private String name;

    @ApiModelProperty(value = "左侧切换展示 0-否 1-是")
    @TableField("left_show")
    private Integer leftShow;

    @ApiModelProperty(value = "跳转url")
    @TableField("redirect_url")
    private String redirectUrl;

    @ApiModelProperty(value = "父类菜单")
    @TableField("parent_permission_code")
    private String parentPermissionCode;

    @ApiModelProperty(value = "排序号")
    @TableField("srt_sort")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    @TableField("srt_description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("extend_field")
    private String extendField;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean bolDeleted;


}
