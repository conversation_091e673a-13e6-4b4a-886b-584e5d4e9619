package cn.harmonycloud.pmp.model.entity.message;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cloudservice_message_type")
@ApiModel(value="云服务消息类型", description="云服务消息类型表")
public class MessageType {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id")
    private int id;

    @ApiModelProperty(value = "云服务名称")
    @TableField(value = "cloudservice_name")
    private String cloudserviceName;

    @ApiModelProperty(value = "消息类型")
    @TableField(value = "message_type")
    private String messageType;

    @ApiModelProperty(value = "消息类型别名")
    @TableField(value = "message_type_alias")
    private String messageTypeAlias;

    @ApiModelProperty(value = "是否为根")
    @TableField(value = "is_root")
    private Boolean isRoot;

    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private int parentId;

    @ApiModelProperty(value = "是否支持机器人")
    @TableField(value = "support_robot")
    private Boolean supportRobot;

    @ApiModelProperty(value = "订阅途径")
    @TableField(value = "subscribe_type")
    private String subscribeType;

    @ApiModelProperty(value = "备注")
    @TableField(value = "tips")
    private String tips;
}
