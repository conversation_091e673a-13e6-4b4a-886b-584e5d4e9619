package cn.harmonycloud.pmp.model.entity;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserMapRole implements Serializable {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;


    public static Map<Long,Long> getMapByList(List<UserMapRole> userMapRoles){
        Map<Long,Long> resultMap = new HashMap<>();
        if(CollUtil.isEmpty(userMapRoles)){
            return resultMap;
        }
        return userMapRoles.stream().collect(Collectors.toMap(UserMapRole::getUserId,UserMapRole::getRoleId));
    }


}
