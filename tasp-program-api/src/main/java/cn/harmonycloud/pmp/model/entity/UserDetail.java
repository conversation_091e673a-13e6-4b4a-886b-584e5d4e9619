package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 钉钉用户信息详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_detail")
@ApiModel(value = "UserDetail对象", description = "用户详情表")
public class UserDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "sys_user表主键id")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "工号")
    @TableField("user_gh")
    private String userGh;

    @ApiModelProperty(value = "手机号")
    @TableField("user_ph")
    private String userPh;
    @ApiModelProperty(value = "姓名")
    @TableField("user_name")
    private String username;

    @ApiModelProperty(value = "部门")
    @TableField("user_dep")
    private String userDep;

    @ApiModelProperty(value = "职位")
    @TableField("user_job")
    @JsonIgnore
    private String userJob;

    @ApiModelProperty(value = "工作地点")
    @TableField("user_workplace")
    private String userWorkplace;

    @ApiModelProperty(value = "年龄")
    @TableField("hire_date")
    private LocalDate hireDate;

    @ApiModelProperty(value = "头像")
    @TableField("user_sex")
    private String userSex;


    @ApiModelProperty(value = "手机")
    @TableField("user_type")
    private String userType;

    @ApiModelProperty(value = "邮箱")
    @TableField("dingding_user_id")
    private String dingdingUserId;

    @ApiModelProperty(value = "证件类型 0身份证 1户口簿；2护照 3军官证 4士兵证 5港澳居民来往内地通行证 6台湾同胞来往内地通行证 7临时身份证 8外国人居留证 9警官证 10其他证件")
    @TableField("sales_task")
    private Long salesTask;


    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
