package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资源类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
public class ResourceGroup implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "资源列表")
    private List<ResourceGroupNode> resourceList;

}
