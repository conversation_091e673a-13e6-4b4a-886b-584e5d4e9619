package cn.harmonycloud.pmp.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 基础属性
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseOperationAuditParam implements Serializable {

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "ip")
    private String ipAddress;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "http返回code")
    private String responseCode;

    @ApiModelProperty(value = "审计对象编号")
    private String operationCode;

    @ApiModelProperty(value = "审计对象id")
    private String operationId;

    @ApiModelProperty(value = "应用信息，id或者code")
    private String appInfo;

    @ApiModelProperty(value = "接口服务开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationStartTime;

    @ApiModelProperty(value = "接口服务结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationEndTime;

    @ApiModelProperty(value = "参数")
    private String param;

    @ApiModelProperty(value = "请求类型")
    private String requestType;

    @ApiModelProperty(value = "请求路径")
    private String requestUrl;

    @ApiModelProperty(value = "跟踪编号")
    private String traceId;

    @ApiModelProperty(value = "请求方法")
    private String method;

    @ApiModelProperty(value = "类名")
    private String className;

    @ApiModelProperty(value = "额外参数")
    private String extendParam;

    @ApiModelProperty(value = "操作名称")
    private String operationName;

    @ApiModelProperty(value = "返回值")
    private String response;

    @ApiModelProperty(value = "返回结果：成功/失败")
    private String result;

    @ApiModelProperty(value = "属性操作")
    private String attribOperation;

}
