package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "租户复合参数对象")
public class OrganizationForceControlDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "ip强制管控 0-否 1-是")
    private Boolean ipForcedControl;
//
//    @ApiModelProperty(value = "gitlab强制管控 0-否 1-是")
//    private Boolean gitlabForcedControl;
//
//    @ApiModelProperty(value = "jenkins强制管控 0-否 1-是")
//    private Boolean jenkinsForcedControl;

}
