package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "租户复合参数对象")
public class OrganizationWhiteIpFlagDto implements Serializable {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "IP白名单开关 0-关 1-开 ")
    private Boolean whiteIpFlag;

    @ApiModelProperty(value = "ip白名单集合")
    private String ipParam;

}
