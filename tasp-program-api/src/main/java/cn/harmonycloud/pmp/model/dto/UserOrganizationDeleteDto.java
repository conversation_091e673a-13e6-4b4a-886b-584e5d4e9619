package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserOrganizationDeleteDto {
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源类型,给资源集下用户分配用户时必填")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化id,给资源集下用户分配用户时必填")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;
}
