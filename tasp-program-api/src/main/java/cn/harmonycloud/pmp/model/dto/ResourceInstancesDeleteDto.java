package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@ApiModel(value="ResourceInstance复合对象", description="ResourceInstance复合对象")
public class ResourceInstancesDeleteDto implements Serializable {

    @ApiModelProperty(value = "租户ID")
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    @NotBlank(message = "资源类型编码不能为空")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    @NotNull(message = "应用资源实例ids不能为空")
    private List<Long> resourceInstanceIds;
}
