package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="资源下查询租户用户对象", description="资源下查询租户用户对象参数")
public class OrganUserTreeNode implements Serializable {

    @ApiModelProperty(value = "节点id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父类id")
    private Long parentId;

    @ApiModelProperty(value = "角色id集合")
    private List<Role> roles;

    public OrganUserTreeNode(Long id, String name, Long parentId) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
    }
}
