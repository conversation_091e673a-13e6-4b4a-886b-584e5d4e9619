package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息收件箱
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Builder
@Data
@TableName("notice_message_inbox")
@ApiModel(value = "MessageInbox对象", description = "消息收件箱")
public class MessageInbox  implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "发送者ID")
    @TableField("send_id")
    private Long sendId;

    @ApiModelProperty(value = "接受者ID，0表示接受者为所有人")
    @TableField("receive_id")
    private Long receiveId;

    @ApiModelProperty(value = "消息ID")
    @TableField("text_id")
    private Long textId;

    @ApiModelProperty(value = "状态0 未读 1已读 2已删除")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

//    @ApiModelProperty(value = "消息类型Id")
//    @TableField("message_from")
//    private Integer messageFrom;
}
