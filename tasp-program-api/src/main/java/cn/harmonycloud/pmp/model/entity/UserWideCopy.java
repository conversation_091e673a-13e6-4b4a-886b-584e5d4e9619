package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_wide_copy")
@ApiModel(value="UserWide对象", description="用户外部应用关联表")
public class UserWideCopy implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "类型 字典表userWide")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "用户ID")
    @TableField("unique_id")
    private String uniqueId;

    @ApiModelProperty(value = "钉钉账号ID")
    @TableField("unique_account_id")
    private Long uniqueAccountId;

    public UserWideCopy(Long userId, Integer type, String uniqueId, Long uniqueAccountId) {
        this.userId = userId;
        this.type = type;
        this.uniqueId = uniqueId;
        this.uniqueAccountId = uniqueAccountId;
    }

    public UserWideCopy(){

    }
}
