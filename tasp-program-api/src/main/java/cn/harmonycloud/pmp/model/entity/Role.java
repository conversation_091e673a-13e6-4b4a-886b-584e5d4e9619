package cn.harmonycloud.pmp.model.entity;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_role")
@ApiModel(value="Role对象", description="角色表")
public class Role implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "编码")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "角色类型编码")
    @TableField("resource_type_code")
    private String resourceTypeCode;

    @ApiModelProperty(value = "是否是管理员角色 0-否 1-是")
    @TableField("is_admin")
    private Integer bolAdmin;

    @ApiModelProperty(value = "1.平台角色 2.租户共享角色 3.全局角色 4.资源类型角色 5.租户角色")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "是否可改 0不可 1可以")
    @TableField("is_editable")
    private Boolean editable;

    @ApiModelProperty(value = "是否可删除 0-否 1-是")
    @TableField("is_deletable")
    private Boolean deletable;

    @ApiModelProperty(value = "备注")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("annotations")
    private String annotations;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁")
    @TableField("version")
    @Version
    private Integer version;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;


}
