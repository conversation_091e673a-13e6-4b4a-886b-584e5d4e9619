package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_tag_relate")
@ApiModel(value="Tag关联对象", description="Tag关联表")
public class TagRelate implements Serializable {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "标签id")
    @TableField("tag_id")
    private Long tagId;

    @ApiModelProperty(value = "关联类型")
    @TableField("relate_type_code")
    private String relateTypeCode;

    @ApiModelProperty(value = "关联实例id")
    @TableField("relate_instance_id")
    private Long relateInstanceId;
}
