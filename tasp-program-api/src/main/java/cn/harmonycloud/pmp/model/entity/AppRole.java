package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@TableName("sys_app_role")
@ApiModel(value="资源之间的角色的关联关系表", description="资源之间的角色的关联关系表")
public class AppRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "来源应用code")
    @TableField("source_app_code")
    private String sourceAppCode;

    @ApiModelProperty(value = "来源资源编号")
    @TableField("source_resource_code")
    private String sourceResourceCode;

    @ApiModelProperty(value = "来源角色id")
    @TableField("source_role_id")
    private Long sourceRoleId;

    @ApiModelProperty(value = "来源角色编号")
    @TableField("source_role_code")
    private String sourceRoleCode;

    @ApiModelProperty(value = "目标应用code")
    @TableField("target_app_code")
    private String targetAppCode;

    @ApiModelProperty(value = "目标资源编号")
    @TableField("target_resource_code")
    private String targetResourceCode;

    @ApiModelProperty(value = "目标角色id")
    @TableField("target_role_id")
    private Long targetRoleId;

    @ApiModelProperty(value = "目标角色编号")
    @TableField("target_role_code")
    private String targetRoleCode;

    @ApiModelProperty(value = "扩展字段")
    @TableField("annotations")
    private String annotations;

}
