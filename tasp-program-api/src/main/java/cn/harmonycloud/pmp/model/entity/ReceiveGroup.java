package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ReceiveGroup
 * @projectName src-message-svc
 * @date 2020/11/4 14:58
 */

@Data
public class ReceiveGroup implements Serializable {

    @ApiModelProperty(value = "租户ID")
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "接收者")
    private List<Receiver> receiverList;
}
