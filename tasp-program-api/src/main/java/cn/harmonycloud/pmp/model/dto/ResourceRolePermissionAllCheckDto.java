package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @describe 角色-权限列表
 * @author: wang<PERSON>an
 * @create: 2022-01-11 17:20:14
 **/
@Data
public class ResourceRolePermissionAllCheckDto implements Serializable {
    private static final long serialVersionUID = 907871241658423700L;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "角色id")
    private Long objectId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "类型 1.角色 2.用户 3.用户组")
    private Integer type;

    @ApiModelProperty(value = "是否选中 0-未选中 1-选中")
    private Boolean checked;
}
