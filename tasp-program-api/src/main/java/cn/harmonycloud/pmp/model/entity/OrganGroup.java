package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class OrganGroup{

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父级名称")
    private String parentName;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "编码")
    private String code;

    public OrganGroup(String name, Integer level, String code,String parentName) {
        this.name = name;
        this.level = level;
        this.code = code;
        this.parentName = parentName;
    }

}
