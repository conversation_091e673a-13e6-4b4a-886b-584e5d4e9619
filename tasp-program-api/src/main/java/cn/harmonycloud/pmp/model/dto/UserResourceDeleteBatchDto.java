package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserResourceDeleteBatchDto {
    @ApiModelProperty(value = "用户id")
    private List<Long> userIds;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "资源类型,")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化ids")
    private List<Long> resourceInstanceIds;

}
