package cn.harmonycloud.pmp.model.entity;

import cn.harmonycloud.pmp.util.IPUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_organization_ip")
@ApiModel(value="租户白名单id对象", description="租户ip白名单")
public class OrganizationIp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "ip白名单")
    @TableField("ip")
    private String ip;

    @ApiModelProperty(value = "网段最小值")
    @TableField("min_ip")
    private Long minIp;

    @ApiModelProperty(value = "网段最大值")
    @TableField("max_ip")
    private Long maxIp;

    public void setIp(String ip) {
        this.ip = ip;
        Map<String,Long> ipGapMap = IPUtil.getIpGap(ip);
        minIp = ipGapMap.get("minIp");
        maxIp = ipGapMap.get("maxIp");
    }
}
