package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
public class ResourceInstanceParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;
}
