package cn.harmonycloud.pmp.model.entity.message;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_robot")
@ApiModel(value="机器人对象", description="机器人表")
public class Robot {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "机器人名称")
    @TableField(value = "robot_name")
    private String robotName;

    @ApiModelProperty(value = "机器人平台 0-钉钉 1-专有钉")
    @TableField(value = "robot_plat")
    private int robotPlat;

    @ApiModelProperty(value = "钉钉账号id")
    @TableField(value = "ding_account_id")
    private Long dingAccountId;

    @ApiModelProperty(value = "机器人状态")
    @TableField(value = "status")
    private Boolean status;

    @ApiModelProperty(value = "webhook")
    @TableField(value = "webhook")
    private String webhook;

    @ApiModelProperty(value = "密钥")
    @TableField(value = "secret")
    private String secret;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
