package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrganResourceRole {

    @ApiModelProperty(value = "角色id集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "租户id集合")
    private List<Long> organIds;

    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "资源id集合")
    private List<Long> resourceIds;
}
