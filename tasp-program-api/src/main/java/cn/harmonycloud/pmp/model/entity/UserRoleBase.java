package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="UserRole基础对象", description="用户角色关联表")
public class UserRoleBase implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "角色集合")
    private List<RoleBase> roleBaseList;

}
