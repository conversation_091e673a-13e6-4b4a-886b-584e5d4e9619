package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_organization")
@ApiModel(value="Organization对象", description="租户表")
public class Organization implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "编码")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "父级部门")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "备注")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("annotations")
    private String annotations;

    @ApiModelProperty(value = "联系人")
    @TableField("contact_person")
    private Long contactPerson;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "IP白名单开关 0-关 1-开")
    @TableField("white_ip_flag")
    private Boolean whiteIpFlag;

    @ApiModelProperty(value = "ip强制管控 0-否 1-是")
    @TableField("ip_forced_control")
    private Boolean ipForcedControl;

    @ApiModelProperty(value = "gitlab强制管控 0-否 1-是")
    @TableField("gitlab_forced_control")
    private Boolean gitlabForcedControl;

    @ApiModelProperty(value = "jenkins强制管控 0-否 1-是")
    @TableField("jenkins_forced_control")
    private Boolean jenkinsForcedControl;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁")
    @TableField("version")
    @Version
    private Integer version;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;


}
