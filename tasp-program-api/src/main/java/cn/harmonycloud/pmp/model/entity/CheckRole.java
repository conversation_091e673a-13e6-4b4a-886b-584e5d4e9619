package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 角色表
 * </p>
 *ø
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class CheckRole implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "1.平台角色 2.租户共享角色 3.全局角色 4.资源类型角色 5.租户角色")
    private Integer type;

    @ApiModelProperty(value = "机构id")
    private Long organId;

    @ApiModelProperty(value = "角色类型编码")
    private String resourceTypeCode;
}
