package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: Record 消息记录表
 * @date 2020/11/4 14:17
 */

@Data
@ToString(callSuper = true)
@ApiModel("消息记录表")
@TableName("hms_record")
public class Record{

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    @TableField("tenant_id")
    private Long tenantId;

    @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;

    @ApiModelProperty(value = "消息内容")
    @TableField("content")
    private String content;

    @ApiModelProperty(value = "发送时间")
    @TableField("send_time")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "类别 字典服务 code= sendType 1代表邮件 2 代表短信 3 站内信  4 钉钉")
    @TableField("send_type")
    private Integer sendType;

    @ApiModelProperty(value = "类别 字典服务 code= sendStatus 1-未发，2-已发，3-发送失败")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "收件者")
    @TableField("receiver")
    private String receiver;

    @ApiModelProperty(value = "模板id")
    @TableField("template_id")
    private Long templateId;

    @ApiModelProperty(value = "发送者名称")
    @TableField("sender")
    private String sender;

    @ApiModelProperty(value = "发送机制  字典服务 code= sendMode 1-定时 2-实时")
    @TableField("send_mode")
    private Integer sendMode;

    @ApiModelProperty(value = "消息来源 字典服务 code= messageSource 1-系统新增 2-后台服务发送")
    @TableField("source")
    private String source;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;
}
