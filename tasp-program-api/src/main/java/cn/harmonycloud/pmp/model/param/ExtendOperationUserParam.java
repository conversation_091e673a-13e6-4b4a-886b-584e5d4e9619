package cn.harmonycloud.pmp.model.param;//package cn.harmonycloud.pmp.model.param;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.io.Serializable;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Builder
//public class ExtendOperationUserParam implements Serializable {
//
//    /**
//     * 自定义操作用户
//     */
//    private String operationUserId;
//
//}
