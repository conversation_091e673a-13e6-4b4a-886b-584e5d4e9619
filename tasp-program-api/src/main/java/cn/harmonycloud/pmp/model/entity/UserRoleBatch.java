package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserRole复合对象")
public class UserRoleBatch implements Serializable {

    @ApiModelProperty(value = "角色ID集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIds;

    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    public UserRoleBatch(Long organId,Long resourceId,List<Long> roleIds, List<Long> userIds) {
        this.roleIds = roleIds;
        this.userIds = userIds;
        this.organId = organId;
    }
}
