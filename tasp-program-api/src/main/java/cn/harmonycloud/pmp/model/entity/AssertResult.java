package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssertResult {
    private final static String SUCCESSED="0";
    private final static String FAILED="1";

    @ApiModelProperty(value = "返回code 0-成功 1-失败")
    private String code;

    public AssertResult(){

    }

    public AssertResult(String code){
        this.code=code;
    }

    public static AssertResult failed(){
        return new AssertResult(FAILED);
    }

    public Boolean isSuccess(){
        return SUCCESSED.equals(this.code);
    }


    public static AssertResult successed(){
        return new AssertResult(SUCCESSED);
    }
}
