package cn.harmonycloud.pmp.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExtendOperationAuditParam implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 自定义扩展参数
     */
    private Map<String,Object> extendParam;
}
