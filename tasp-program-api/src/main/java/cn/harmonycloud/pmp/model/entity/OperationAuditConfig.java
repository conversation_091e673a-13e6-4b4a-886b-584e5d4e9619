package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("sys_operation_audit_config")
@ApiModel(value="OperationAuditConfig对象", description="操作审计配置表")
public class OperationAuditConfig {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;



    /**
    * 操作事项实体编号
    */
    @TableField("operation_code")
    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    /**
    * 记录清除周期 单位:天
    */
    @TableField("clearance_cycle")
    @ApiModelProperty(value = "记录清除周期")
    private Integer clearanceCycle;

    /**
    * 是否归档 0-是 1-否
    */
    @TableField("archive")
    @ApiModelProperty(value = "是否归档")
    private Integer archive;

    /**
    * 设置类型 0-通用类型 1-操作事项
    */
    @TableField("config_type")
    @ApiModelProperty(value = "设置类型")
    private Integer configType;

    /**
    * 创建人
    */
    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
    * 创建时间
    */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
    * 更新人
    */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

}