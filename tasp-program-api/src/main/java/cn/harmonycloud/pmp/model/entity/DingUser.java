package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@SuperBuilder
@TableName("sys_ding_user")
public class DingUser {

    public DingUser() {
    }

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "钉钉id")
    @TableField(value = "ding_id")
    private String dingId;

    @ApiModelProperty(value = "钉钉企业id")
    @TableField(value = "ding_account_id")
    private Long dingAccountId;

    @ApiModelProperty(value = "账号类型")
    @TableField(value = "user_type")
    private Integer userType;

    @ApiModelProperty(value = "名字")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(value = "电话")
    @TableField(value = "phone")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    @TableField(value = "email")
    private String email;

    @ApiModelProperty(value = "父部门id")
    @TableField(value = "parent_dept_id")
    private String parentDeptId;
}
