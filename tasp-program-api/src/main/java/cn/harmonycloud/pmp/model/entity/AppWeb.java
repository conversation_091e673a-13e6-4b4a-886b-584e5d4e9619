package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 应用微前端关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_app_web")
@ApiModel(value="AppWeb对象", description="应用微前端关联关系表")
public class AppWeb implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "应用id")
    @TableField("app_id")
    private Long appId;

    @ApiModelProperty(value = "微前端id")
    @TableField("web_id")
    private Long webId;

    @ApiModelProperty(value = "排序号")
    @TableField("sort_id")
    private Integer sortId;

    @ApiModelProperty(value = "菜单id")
    @TableField("permission_id")
    private Long permissionId;

    @ApiModelProperty(value = "菜单等级 1.开放 2.授权")
    @TableField("permission_level")
    private Boolean permissionLevel;


}
