package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="权限树状节点对象", description="权限树状节点对象")
public class PermissionTreeNode implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;

    @ApiModelProperty(value = "请求方式 1.GET 2.POST 3.PUT 4.DELETE")
    private Integer method;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "应用ID")
    private Long appId;

    @ApiModelProperty(value = "是否可见")
    private Boolean visible;

    @ApiModelProperty(value = "api")
    private String api;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "子应用菜单是否由主应用控制")
    private Integer frameControl;

    @ApiModelProperty(value = "接口列表")
    private String url;

    @ApiModelProperty(value = "类型 1.菜单 2.页面元素")
    private Integer type;

    @ApiModelProperty(value = "种类 1.平台菜单 2.租户管理菜单 3.应用菜单")
    private Integer kind;

    @ApiModelProperty(value = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "")
    private Boolean isIframe;

    @ApiModelProperty(value = "资源类型编码")
    private String resourceTypeCode;

    @ApiModelProperty(value = "")
    private String iframeUrl;

}
