package cn.harmonycloud.pmp.model.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/12/2 11:50
 */
public interface DictCons {

    @Getter
    @AllArgsConstructor
    enum NoticeStatus {
        UNREAD("未读", 0),
        READ("已读", 1),
        DELETED("已删除", 2),
        CONFIRMED("通过", 3),
        REJECTED("拒绝", 4),
        WITHDRAWN("撤回",5),
        ;
        private final String name;
        private final int value;
    }


    @Getter
    @AllArgsConstructor
    enum MessageStatus {
        UNSEND("待发送", 0),
        SEND("已发送", 1),
        WITHDRAWN("已撤回", 2),
        ;
        private final String name;
        private final int value;
    }

    /**
     * 消息是否全部选中
     */
    @Getter
    @AllArgsConstructor
    enum MessageChoosedIsAll {
        ALL("全选", 1),
        PART("部分选中", 2),
        ;
        private final String name;
        private final int value;
    }

    /**
     *
     */
    @Getter
    @AllArgsConstructor
    enum SendModule  {
        Front("系统新增", 1),
        Back("后台服务发送", 2),
        ;
        private final String name;
        private final int value;
    }
}
