package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class UserResourceUpdateRoleDto {
    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    @NotBlank(message = "资源类型编码不能为空")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    @NotNull(message = "应用资源id不能为空")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户信息不能为空")
    private Long userId;

    @ApiModelProperty(value = "要修改的角色id")
    @NotNull(message = "角色不能为空")
    private List<Long> roleIds;


    public ResourceRole createResourceRole(){
        ResourceRole resourceRole = new ResourceRole();
        resourceRole.setOrganId(this.organId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        return resourceRole;
    }
}
