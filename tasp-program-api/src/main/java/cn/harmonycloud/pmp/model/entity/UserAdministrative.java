package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 人员行政组织关系
 * <AUTHOR>
 * @Date 2024/7/8 3:20 下午
 **/
@Data
@TableName("sys_user_administrative")
@ApiModel(value="人员行政组织关联关系表", description="人员行政组织关联关系表")
public class UserAdministrative implements Serializable {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "租户id")
    private Long userId;

    @ApiModelProperty(value = "行政组织")
    private Long adminId;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
