package cn.harmonycloud.pmp.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class TagAddDto implements Serializable {

    @ApiModelProperty(value = "标签编号",hidden = true)
    private String code;

    @ApiModelProperty(value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下")
    @NotNull(message = "范围不能为空")
    private Integer scope;

    @ApiModelProperty(value = "标签名")
    @NotBlank(message = "标签名不能为空")
    private String name;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "标签实体类型")
    private String typeCode;

    @ApiModelProperty(value = "标签实体实例id")
    private String instanceId;

    @ApiModelProperty(value = "应用code",hidden = true)
    private String appCode;

    @ApiModelProperty(value = "标签分类id",hidden = true)
    private Long classificationId;

    @ApiModelProperty(value = "标签分类编号")
    private String classificationCode;

    @ApiModelProperty(value = "颜色")
    private String colour;

    public void setName(String name) {
        this.name = name;
        this.code = name;
    }
}
