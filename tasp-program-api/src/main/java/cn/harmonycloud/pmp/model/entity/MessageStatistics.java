package cn.harmonycloud.pmp.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: MessageStatistics
 * @projectName src-message-svc
 * @date 2020/11/10 16:07
 */

@Data
@ToString(callSuper = true)
@TableName("hms_message_statistics")
public class MessageStatistics{
    /**
     * 消息类型（SMS/MAIL）
     */
    private Integer sendType;
    /**
     * 发送数量
     */
    private int number;

    /**
     * 关联的租户id
     */
    private Long relationTenantId;
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;
}
