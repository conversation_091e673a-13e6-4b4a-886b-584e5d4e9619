package cn.harmonycloud.pmp.model.entity;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class DingTalkUser implements Serializable {

    private static final long serialVersionUID = 1L;
    String errcode;
    String errmsg;
    String request_id;
    Map<String,String> result;


    public String getUniqueId(){
        String unique = null;
        if(ObjectUtil.isNotNull(this.result)){
            unique=result.get("userid");
        }
        return unique;
    }
}
