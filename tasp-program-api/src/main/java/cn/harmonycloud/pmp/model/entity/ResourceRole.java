package cn.harmonycloud.pmp.model.entity;

import cn.harmonycloud.pmp.constant.AmpConstant;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ResourceRole implements Serializable {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "主键ID",hidden = true)
    private Long resourceId=0L;

    @ApiModelProperty(value = "主键ID集合",hidden = true)
    private List<Long> resourceIds;

    @ApiModelProperty(value = "资源类型")
    private List<String> resourceTypeCodes;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "父类资源id",hidden = true)
    private Long parentResourceId;

    @ApiModelProperty(value = "父类资源类型")
    private String parentResourceTypeCode;

    @ApiModelProperty(value = "父类应用资源id")
    private Long parentResourceInstanceId;

    @ApiModelProperty(value = "应用id")
    private Long appId;

    @ApiModelProperty(value = "资源拥有者类型 1-人员 2-角色 支持子应用扩展")
    private String typeDictValue;

    @ApiModelProperty(value = "资源拥有者id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "资源实例名称")
    private String resourceInstanceName;

    public boolean isResourceRole(){
        return ObjectUtil.isNotNull(resourceInstanceId) || !AmpConstant.ORGAN_RESOURCE_ID.equals(resourceId) || ObjectUtil.isNotNull(parentResourceInstanceId);
    }

    public static ResourceRole getParentResourceRole(ResourceRole resourceRole){
        ResourceRole parentResourceRole = new ResourceRole();
        parentResourceRole.setResourceInstanceId(resourceRole.parentResourceInstanceId);
        parentResourceRole.setResourceTypeCode(resourceRole.parentResourceTypeCode);
        parentResourceRole.setOrganId(resourceRole.organId);
        parentResourceRole.setAppId(resourceRole.appId);
        return parentResourceRole;
    }
}
