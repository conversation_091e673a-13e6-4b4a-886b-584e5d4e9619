package cn.harmonycloud.pmp.model.entity;

import cn.harmonycloud.pmp.constant.DictCons.PermissionType;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_permission")
@ApiModel(value="Permission对象", description="权限表")
public class Permission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "父级ID")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "编码")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "类型 1.菜单 2.按钮 3.tab页")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "种类 1.平台菜单 2.租户菜单 3.资源菜单")
    @TableField("kind")
    private Integer kind;

    @ApiModelProperty(value = "应用ID")
    @TableField("app_id")
    private Long appId;

    @ApiModelProperty(value = "图标")
    @TableField("icon")
    private String icon;

    @ApiModelProperty(value = "请求方式 1.GET 2.POST 3.PUT 4.DELETE")
    @TableField("method")
    private Integer method;

    @ApiModelProperty(value = "是否可见")
    @TableField("visible")
    private Boolean visible;

    @ApiModelProperty(value = "接口列表")
    @TableField("url")
    private String url;

    @ApiModelProperty(value = "api接口")
    @TableField("api")
    private String api;

    @ApiModelProperty(value = "排序号")
    @TableField("sort_id")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    @TableField("annotations")
    private String annotations;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "资源类型编码")
    @TableField("resource_type_code")
    private String resourceTypeCode;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁")
    @TableField("version")
    @Version
    private Integer version;

    @ApiModelProperty(value = "逻辑删除 0正常 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty(value = "是否是iframe")
    @TableField("is_iframe")
    private Boolean isIframe;

    @ApiModelProperty(value = "iframe url")
    @TableField("iframe_url")
    private String iframeUrl;

    /**
     * 递归获取所有子菜单
     * @param childPermissions
     * @return
     */
    public static Map<String,Object> reGetPermission(List<Permission> childPermissions,Long parentId,String parentCode){
        Map<String,Object> mapResult = new HashMap<>();
        Map<Integer,List<Permission>> groupMap = childPermissions.stream().filter(permission -> parentId.equals(permission.getParentId())).collect(Collectors.groupingBy(Permission::getType));
        List<Permission> menus = groupMap.get(PermissionType.PERMISSION.getValue());
        List<Permission> buttons = groupMap.get(PermissionType.BUTTON.getValue());
        if(CollUtil.isNotEmpty(buttons)){
            mapResult.put(parentCode,dos2Map(buttons));
        }
        if(CollUtil.isNotEmpty(menus)){
            for(Permission permission:menus){
                mapResult.putAll(reGetPermission(childPermissions,permission.getId(),permission.getCode()));
            }
        }
        return mapResult;
    }

    /**
     * 将type-2页面元素的组装成map
     * @param permissions
     * @return
     */
    private static Map dos2Map(List<Permission> permissions){
        Map map =new HashMap();
        permissions= Optional.ofNullable(permissions).orElse(CollUtil.newArrayList());
        permissions.stream().forEach(permission -> {
            map.put(permission.getCode(),true);
        });
        return map;
    }

    /**
     * 组装权限
     * @param permissions
     * @return
     */
    public static Map<String,Object> permissionMap(List<Permission> allPermissions,List<Permission> permissions){
        Map<String,Object> map =new HashMap();
        List<Long> permissionIds= permissions.stream().map(Permission::getId).collect(Collectors.toList());
        //组装功能点
        allPermissions.stream()
            .filter(permission -> PermissionType.BUTTON.getValue().equals(permission.getType()))
            .forEach(permission -> {
                if(permissionIds.contains(permission.getId())){
                    map.put(permission.getCode(),true);
                }else {
                    map.put(permission.getCode(),false);
                }
            });
        //组装tab页
        List<Map> list = CollUtil.newArrayList();
        allPermissions.stream()
            .filter(permission -> PermissionType.TAB.getValue().equals(permission.getType()))
            .forEach(permission -> {
                Map<String,Object> tmp = new HashMap<>();
                tmp.put("name",permission.getName());
                tmp.put("id",permission.getId());
                tmp.put("code",permission.getCode());
                if(permissionIds.contains(permission.getId())){
                    tmp.put("disabled",true);
                }else {
                    tmp.put("disabled",false);
                }
                list.add(tmp);
            });
        map.put("tab",list);
        return map;
    }


}
