package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class PermissionIdsByOrganAndShareRoleDto implements Serializable {


    @ApiModelProperty(value = "对象ID")
    private Long objectId;

    @ApiModelProperty(value = "类型 1.角色 2.用户 3.用户组")
    private Integer type;

    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "应用id")
    private Long appId;

    @ApiModelProperty(value = "应用id")
    private List<Integer> permissionTypeList;
}
