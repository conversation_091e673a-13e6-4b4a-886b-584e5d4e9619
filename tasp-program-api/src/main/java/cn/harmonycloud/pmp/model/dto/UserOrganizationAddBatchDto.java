package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="UserOrganization复合对象")
public class UserOrganizationAddBatchDto implements Serializable {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @NotBlank(message = "实例类型不能为空")
    @ApiModelProperty(value = "实例类型")
    private String resourceTypeCode;

    @NotNull(message = "实例id集合不能为空")
    @ApiModelProperty(value = "实例id集合")
    private List<Long> resourceInstanceIds;

    @NotNull(message = "用户不能为空")
    @ApiModelProperty(value = "用户集合")
    private List<Long> userIds;

    @NotNull(message = "角色不能为空")
    @ApiModelProperty(value = "角色id集合")
    private List<Long> roleIds;
}
