package cn.harmonycloud.trinasolar.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - DevOps流水线构建历史 Response 的 VO")
@Data
@ToString(callSuper = true)
public class DevOpsPipelineHisRespVO {
    /**
     * 构建号
     */
    private long buildNum;
    /**
     * 构建信息
     */
    private String buildMsg;
    /**
     * 执行记录ID
     */
    private String id;
    /**
     * 执行记录状态
     */
    private String status;
    /**
     * 开始构建时间
     */
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;
    /**
     * 构建完成时间
     */
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
    /**
     * 总耗时(毫秒)
     */
    private int totalTime;
    /**
     * 运行耗时(毫秒，不包括人工审核时间)
     */
    private int executeTime;
    /**
     * 启动用户
     */
    private String userId;
    /**
     * 构建参数，暂不展示
     */
  //  private JSONArray buildParameters;

}
