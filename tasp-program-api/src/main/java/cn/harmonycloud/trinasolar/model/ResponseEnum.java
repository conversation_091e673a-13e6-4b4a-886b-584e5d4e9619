package cn.harmonycloud.trinasolar.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应枚举
 * 通用返回结果枚举
 *
 * <AUTHOR>
 * @version v1.0.0
 * @date 2022-03-12 23:15:11
 * @since 2019-07-19 09:39
 */
@Getter
@AllArgsConstructor
public enum ResponseEnum {
    /**
     * 当前租户数据源不存在
     */
    TENANT_ERROR(9996, "当前租户数据源不存在"),

    // ----------------------------------------------------------------Server Error and Exception Enum start
    /**
     * 成功
     */
    SUCCESS(0, "SUCCESS"),
    /**
     * 失败
     */
    FAILED(1, "FAILED"),

    /**
     * 服务器异常，无法识别的异常，尽可能对通过判断减少未定义异常抛出
     */
    SERVER_ERROR(9999, "网络异常"),
    /**
     * 服务器繁忙，请稍后重试
     */
    SERVER_BUSY(9998, "服务器繁忙"),
    /**
     * 参数绑定校验异常
     */
    PARAM_VALID_ERROR(9997, "参数绑定校验异常"),
    /**
     * 系统默认数据不能操作，只能查看
     */
    SYS_LEVEL_DATA(9996, "系统数据禁止操作"),

    /**
     * 系统内 Feign 调用失败
     */
    SYS_FEIGN_FAILED(9995, "Feign 调用失败"),

    /**
     * 接口请求频繁
     */
    SYS_REQUEST_TOO_MANY(9994, "接口请求频繁，请稍后重试"),
    /**
     * 数据权限接口格式不规范
     */
    SYS_DATA_RULE_BODY_IRREGULAR(9993, "数据权限接口格式不规范"),

    SERVER_HEADER_SECRET_KEY(9981, "秘钥获取失败"),
    SERVER_HEADER_APP_IDS(9982, "应用IDS获取失败"),
    SERVER_HEADER_ORG(9982, "组织机构获取失败"),
    SERVER_HEADER_TOKEN(9982, "TOKEN获取失败"),
    SERVER_HEADER_USER(9982, "用户获取失败"),

    // ----------------------------------------------------------------Server Error and Exception Enum end

    // ----------------------------------------------------------------Login Error and Exception Enum start
    LOGIN_ACCOUNT_NOT_FOUND(9910, "该账户不存在！"),
    LOGIN_PASSWORD_ERROR(9911, "该账户密码错误！"),
    LOGIN_ERROR(9919, "认证失败，您的用户名或密码错误，您还有N次重试机会！"),
    LOGIN_ERROR_APP(9938, "该账户不支持登录当前应用！"),
    LOGIN_TIME_ERROR(9929, "登录超时，本地计算机时间错误（与服务器时间差超过10分钟），请更新设置本地计算机时间！"),
    LOGIN_VALIDATE_CODE_ERROR(9912, "验证码错误！"),
    LOGIN_ACCOUNT_DISABLED(9913, "该账户已被禁用，请联系管理员！"),
    LOGIN_ACCOUNT_LOCKED(9914, "该账号已被锁定，请联系管理员！"),
    LOGIN_ACCOUNT_EXPIRED(9915, "该账号已过期，请联系管理员！"),
    LOGIN_PERMISSION_DENIED(9916, "您没有访问权限，请联系管理员！"),
    LOGIN_ACCOUNT_ALREADY(9917, "该账户已登录，请不要重复登录！"),
    LOGIN_APP_OUT_NUM(9918, "该应用登录访问人数已超限，请联系管理员！"),
    LOGIN_LOGOUT_FAILED(9920, "退出失败，Token 为空！"),
    LOGIN_VERIFY_FAILED(9921, "验证失败，Token 为空！"),
    LOGIN_TOKEN_EXPIRED(9922, "Token 已过期，请重新登录！"),
    LOGIN_ACCOUNT_UNAUTHORIZED(9923, "未登录！"),
    LOGIN_APP_PERSON_LIMIT(9924, "应用登录人数受限！"),
    LOGIN_APP_NOT_FOUND(9925, "应用未注册，请联系应用相关负责人！"),
    LOGIN_APP_URL_ERROR(9926, "应用地址错误！"),
    LOGIN_ACCOUNT_PHONE_EMPTY(9927, "该账户手机号为空！"),
    LOGIN_ACCOUNT_NEW_SERIAL(9928, "新设备登录！"),
    LOGIN_CODE_ERROR(9930, "参数编码错误"),
    LOGIN_COUNT_ERROR(9931, "您已登录失败5次，账号已被锁定，请20分钟后再试！"),
    LOGIN_PASSWORD_SIMPLE_ERROR(9932, "密码过于简单，请修改密码"),
    LOGIN_REFRESH_NOT_NULL(99333, "刷新token失败，refresh_token不能为空"),
    LOGIN_TENANT_NOT_AUTH(9935, "系统正在检修中！"),
    // ----------------------------------------------------------------Login Error and Exception Enum end

    // ----------------------------------------------------------------Upms service Error and Exception Enum start
    UPMS_USER_EXIST(9800, "用户已存在"),
    UPMS_USER_ID_EMPTY(9801, "用户id不能为空"),
    UPMS_USER_OLD_PASSWORD_ERROR(9802, "原密码输入错误"),
    UPMS_USER_UPDATE_PASSWORD_SUCCESS(9803, "密码修改成功，请重新登录"),
    UPMS_USER_RESET_PASSWORD_SUCCESS(9804, "重置密码成功"),
    UPMS_USER_UPLOAD_AVATAR_FAILED(9805, "用户头像上传失败"),
    UPMS_USER_NAME_EMPTY(9806, "用户名不能为空"),
    UPMS_USER_CODE_EXIST(9807, "用户编码已存在"),
    UPMS_USER_UNITY_ACCOUNT_ADD(9808, "只能新建AD账号"),
    UPMS_CLOUD_PER_DEL_FAILED(9821, "菜单含有下级不能删除"),
    UPMS_APP_PER_DEL_FAILED(9822, "菜单含有下级不能删除"),
    UPMS_ORG_ID_EMPTY(9823, "组织机构id不能为空"),
    UPMS_ORG_CHILDREN_NOT_EMPTY(9824, "该组织机构存在子节点，确认删除？"),
    UPMS_APP_PERMISSION_SWAGGER_API_NON(9825, "该服务swagger文档错误"),
    UPMS_APP_PERMISSION_API_PARAM_ONLY(9826, "字段不唯一"),
    UPMS_ROLE_NAME_EXIST(9827, "角色名称或编码已存在"),
    UPMS_PERMISSION_NAME_EXIST(9828, "权限名称已存在"),
    UPMS_PERMISSION_USER_EMPTY(9829, "用户没有分配菜单权限"),
    UPMS_ROLE_NOT_EXIST(9830, "角色不存在"),
    GATEWAY_DATAPERMISSION_FILTER_FAILED(9831, "数据过滤失败"),
    UPMS_SESSION_CONF_APP_ALREADY(9832, "该应用会话配置已存在"),
    UPMS_USER_NOT_FOUND(9833, "用户不存在"),
    UPMS_POST_NOT_FOUND(9834, "岗位不存在"),
    UPMS_POST_CODE_EXIST(9850, "岗位编码已存在"),
    UPMS_POST_USER_ORG_CONFLICT(9851, "成员与岗位绑定组织不匹配，请确认后新增"),
    UPMS_APP_REMOTE_P_M_EMPTY(9834, "创建应用菜单失败"),
    UPMS_PERMISSION_NOT_EXIST(9835, "权限不存在"),
    UPMS_DATA_RULE_PATH_EXIST(9836, "该ApiPath的数据权限已存在"),
    UPMS_USER_IMPORT_FAILED(9837, "用户导入失败"),
    UPMS_USER_IMPORT_NOT_EMPTY(9838, "导入用户不能为空"),
    UPMS_USER_IMPORT_DATA_ERROR(9839, "导入用户数据错误"),
    UPMS_ROLE_PERMISSION_NOT_SELECT(9840, "未选择权限"),
    UPMS_ROLE_APP_ONLY(9841, "独立应用角色只能该应用功能权限"),
    UPMS_ORG_NOT_EXIST(9842, "组织机构不存在"),
    UPMS_ORG_NOT_EXIST_PARENT(9843, "当前组织机构不存在父级组织"),
    UPMS_ORG_EXIST_CHILD(9844, "当前组织机构存在子组织，请先处理子组织机构"),
    UPMS_ORG_EXIST_USER(9845, "当前组织机构或下级组织下存在用户，请先处理用户数据"),

    UPMS_ROLE_ID_EMPTY(9846, "角色ID不能为空"),
    UPMS_ROLE_NAME_EMPTY(9847, "角色名称不能为空"),
    UPMS_ROLE_CODE_EMPTY(9848, "角色编码不能为空"),
    UPMS_ROLE_EXPIRE_TIME_EMPTY(9849, "角色过期时间不能为空"),

    UPMS_ROLE_SCOPE_EXIST(9900, "角色作用域已存在"),
    UPMS_ROLE_SCOPE_SOURCE_TYPE_NOT_NULL(9901, "作用域资源类型不能为空"),
    UPMS_ROLE_SCOPE_NOT_NULL(9902, "作用域ID不能为空"),
    UPMS_ROLE_SCOPE_NAME_EXIST(9903, "作用域名称已存在"),
    UPMS_ROLE_SCOPE_ID_NOT_NULL(9904, "作用域ID不能为空"),
    UPMS_ROLE_SCOPE_ORG_NOT_NULL(9905, "作用域组织机构不能为空"),
    UPMS_ROLE_USER_SCOPE_IS_NULL(9906, "当前用户没有分配管理权限"),

    UPMS_TENANT_NOT_FOUND(10000, "租户不存在"),
    UPMS_TENANT_HAS_FINISHED(10001, "租户已完成初始化"),

    UPMS_TENANT_CODE_EXIST(10000, "租户编码已存在"),

    UPMS_GROUP_TYPE_IS_NOT_NULL(9930, "分组类型不能为空"),
    UPMS_GROUP_EXIST(9931, "分组已存在"),
    UPMS_GROUP_NAME_IS_NOT_NULL(9932, "分组名称不能为空"),
    UPMS_GROUP_IS_SYS(9933, "系统默认分组不能删除"),
    UPMS_GROUP_IS_USED(9934, "分组(子分组)已被使用，请先删除分组关系"),

    UPMS_LABEL_EXIST(9940, "标签已存在"),
    UPMS_LABEL_NAME_IS_NOT_NULL(9941, "标签名称不能为空"),
    UPMS_LABEL_GROUP_IS_NOT_NULL(9942, "标签分组不能为空"),
    UPMS_LABEL_RULE_IS_NOT_NULL(9943, "标签规则不能为空"),
    UPMS_LABEL_IS_NOT_IDENTICAL(9944, "用户标签不一致"),
    UPMS_LABEL_IS_USED(9945, "标签已被使用,请先删除标签关系"),
    UPMS_LABEL_RULE_IS_IDENTICAL(9946, "标签规则不能重复"),
    // ----------------------------------------------------------------Upms service and Exception Enum end

    // ----------------------------------------------------------------notice service Error and Exception Enum start
    NOTICE_METADATA_TARGET_ACTION_ALREADY(97001, "事件已存在"),
    NOTICE_METADATA_NOT_FOUND(97002, "事件不存在"),
    NOTICE_SUB_ALREADY(97003, "当前订阅对象的订阅已存在"),
    NOTICE_SUB_NOT_FOUND(97004, "订阅配置不存在"),
    NOTICE_NOTIFY_NOT_FOUND(97005, "消息不存在"),
    NOTICE_NOTIFY_ALREADY_RELEASE(97006, "消息已发布，不能修改删除"),
    NOTICE_NOTIFY_MESSAGE_ORG_USER(97007, "消息绑定的组织机构和人不能都为空"),
    NOTICE_SUB_NAME_ALREADY(97008, "订阅名称已存在"),
    NOTICE_EVENT_SOURCE_PRE_ALREADY(97009, "预注册事件已存在"),
    NOTICE_EVENT_SOURCE_NOTICE_NOT_FOUND(97010, "消息绑定事件不存在"),
    NOTICE_EVENT_SOURCE_APP_NOT_FOUND(97011, "事件绑定的应用不存在"),
    NOTICE_EVENT_SOURCE_SVC_NOT_FOUND(97012, "事件绑定的服务不存在"),
    NOTICE_EVENT_SOURCE_COMP_NOT_FOUND(97013, "事件绑定的组件不存在"),
    NOTICE_SUB_LIST_EMPTY(97014, "新建订阅为空"),
    // ----------------------------------------------------------------notice service and Exception Enum end

    // ----------------------------------------------------------------integration service Error and Exception Enum start
    INTE_APP_EN_NAME_REPEAT(9301, "应用英文名称已存在!"),
    INTE_CREATE_APP_PERMISSION_FAIL(9302, "创建应用权限失败!"),
    INTE_CREATE_APP_PERMISSION_FAIL_(9302, "创建应用权限失败! 子权限不来自于父权限"),
    INTE_SVC_FRONT_EN_NAME_REPEAT(9303, "前端英文名称已存在!"),
    INTE_SVC_LOGIC_EN_NAME_REPEAT(9304, "后端英文名称已存在!"),
    INTE_UPDATE_APP_PERMISSION_FAIL(9305, "更新应用权限失败!"),
    INTE_APP_PUBLISH_FAIL(9306, "发布应用失败!"),
    INTE_APP_REFRESH_ROUTE_FAIL(9307, "发布应用，更新路由失败!"),
    INTE_CLOUD_CPU_QUOTA_LESS(9308, "观云台CPU资源不足!"),
    INTE_CLOUD_MEMORY_QUOTA_LESS(9309, "观云台内存资源不足!"),
    INTE_FRONT_URL_REG_MATCH_FAIL(9310, "前端服务访问地址格式有误!"),
    INTE_LOGIC_URL_REG_MATCH_FAIL(9311, "后端服务访问地址格式有误!"),
    INTE_UPDATE_SVC_PERMISSION_FAIL(9311, "更新服务权限失败!"),
    INTE_INSERT_SVC_PERMISSION_FAIL(9312, "新建服务权限失败!"),
    INTE_DELETE_SVC_PERMISSION_FAIL(9313, "删除服务权限失败!"),
    INTE_APP_ONLINE_FAIL(9314, "应用上线失败!"),
    INTE_APP_OFFLINE_FAIL(9315, "应用下线失败!"),
    INTE_CASCADE_DEL_CPON_FAIL(9316, "级联解除组件绑定失败!"),
    INTE_QUERY_COMPONENT_INFO_FAIL(9317, "获取组件信息失败!"),
    INTE_APP_NOT_FOUND(9318, "获取应用失败!"),
    INTE_APP_UPDATE_FAIL(9319, "更新应用失败!"),
    INTE_SVC_NOT_FOUND(9320, "获取服务失败!"),
    INTE_USER_APP_NOT_FOUND(9321, "当前用户暂无应用!"),
    INTE_CLAIM_SYS_REPEAT(9322, "应用已存在!"),
    INTE_CLAIM_APP_REPEAT(9323, "服务已存在!"),
    INTE_CLAIM_COMPONENT_REPEAT(9324, "组件已存在!"),
    INTE_CLAIM_SYS_NO_REPEAT(9325, "应用不存在!"),
    INTE_CLAIM_APP_NO_REPEAT(9326, "服务不存在!"),
    INTE_CLAIM_COMPONENT_NO_REPEAT(9327, "组件不存在!"),
    INTE_CLAIM_NO_REPEAT(9328, "删除项不存在!"),
    INTE_CLAIM_NO_TYPE(9329, "不支持的外部应用或服务或组件!"),
    INTE_CLAIM_SYS_HOME_PAGE_NOT_BLANK(9330, "应用的访问地址不能为空!"),
    INTE_CLAIM_SYS_SERVER_URL_NOT_BLANK(9331, "后台地址不能为空!"),
    INTE_CLAIM_SYS_SWAGGER_URL_NOT_BLANK(9332, "Swagger文档接口地址不能为空!"),
    // ----------------------------------------------------------------integration service and Exception Enum end

    // ----------------------------------------------------------------activity service Error and Exception Enum start
    ACTI_REPEAT_AUDIT_COMPONENT(9400, "该组件已被审核，请勿重复审核!"),
    ACTI_GET_USER_INFO_FAIL(9401, "获取用户失败!"),
    ACTI_PROCESS_INSTANCE_ERROR(9402, "流程实例不存在!"),
    // ----------------------------------------------------------------activity service and Exception Enum end

    // ----------------------------------------------------------------component service Error and Exception Enum start
    CPON_FUNCTION_REPORT_FAIL(9500, "功能测试报告尚未通过!"),
    CPON_PERFORMANCE_REPORT_FAIL(9501, "性能测试报告尚未通过!"),
    CPON_SERIAL_NO_REPEAT(9502, "编号已存在!"),
    CPON_NOT_FOUND(9503, "组件不存在!"),
    CPON_PROJECT_NOT_FOUND(9504, "项目不存在!"),
    CPON_PAGE_NOT_FOUND(9505, " 找不到该页面!"),
    CPON_GITLAB_ACCOUNT_NOT_FOUND(9506, "gitlab账号不存在，请先创建账号!"),
    CPON_PROJECT_EN_NAME_REPEAT(9507, "项目英文名称已存在，请重新输入!"),
    CPON_PAGE_EN_NAME_REPEAT(9508, "页面英文名称已存在，请重新输入!"),
    CPON_APPLY_USER_OWN_ERROR(9509, "您是组件开发者，无需申请即可使用!"),
    CPON_APPLY_USER_OTHER_ERROR(9510, "您已申请过该组件，请勿重复申请!"),
    CPON_APPLY_NOT_REPEAT(9510, "您申请的组件正在审核中，请勿重复申请!"),
    CPON_CLOUD_CANARY_UPDATE_FINISH(9511, "观云台灰度升级已完毕!"),
    CPON_USER_KEY_NOT_FOUND(9512, "当前用户没有组件秘钥"),
    // ----------------------------------------------------------------component service and Exception Enum end

    // ----------------------------------------------------------------cloud Error and Exception Enum start
    CLOUD_CONTAINER_NOT_FOUND(100110, "观云台获取容器信息异常!"),
    CLOUD_CONFIG_NOT_FOUND(100111, "观云台获取配置信息异常!"),
    CLOUD_CICD_EXECUTE_FAIL(100112, "观云台流水线执行失败!"),
    CLOUD_CLUSTER_CN_NAME_EXIST(100113, "服务节点中文已存在!"),
    CLOUD_CLUSTER_EN_NAME_EXIST(100114, "服务节点英文已存在!"),
    CLOUD_CLUSTER_HOST_IP_EXIST(100115, "服务节点集群IP已存在!"),
    CLOUD_CLUSTER_NOT_EXIST(100115, "服务节点不存在!"),
    CLOUD_CPU_RESOURCE_NOT_ENOUGH(100116, "观云台CPU资源不足，请联系管理员!"),
    CLOUD_MEMEORY_RESOURCE_NOT_ENOUGH(100117, "观云台内存资源不足，请联系管理员!"),
    // ----------------------------------------------------------------cloud Error and Exception Enum end

    // ----------------------------------------------------------------cloud Error and Exception Enum start
    PROD_DISTRIBUTE_APP_NOT_FOUND(110110, "派发应用不存在!"),
    PROD_CLOUD_APP_NOT_FOUND(110110, "派发应用对应观云台应用不存在!"),
    PROD_DISTRIBUTE_SVC_NOT_FOUND(110111, "派发应用服务不存在!"),
    PROD_DISTRIBUTE_APP_ABBREVIATE_REPEAT(110112, "派发应用简称已存在!"),
    // ----------------------------------------------------------------cloud Error and Exception Enum end

    // ----------------------------------------------------------------common Error and Exception Enum start
    REMOTE_CALL_ERROR(9000, "服务调用异常"),
    CN_NAME_REPEAT(9001, "中文名称已存在!"),
    EN_NAME_REPEAT(9002, "英文名称已存在!"),
    EN_NAME_ERROR(9008, "英文名称不符合规范!"),

    CPON_NAME_NOT_EQUALS_ZIP(9003, "组件英文名和压缩包名称不一致!"),
    CLUSTER_DELETE_ERROR(9004, "集群有绑定的派发应用，不能删除"),
    CLUSTER_BIND_ERROR(9005, "集群已绑定详情信息，请勿重复绑定！！"),
    CLUSTER_NOT_DISPATCH(9006, "未找到分配的集群！！"),
    CLUSTER_DETAIL_NOT_FOUND(9007, "集群详细信息未填写！！"),
    CPON_RELY_ERROR(9008, "依赖组件错误"),


    LOG_MOBILE_ONLINE_FAILED(8700, "获取移动端在线人数失败"),
    // ----------------------------------------------------------------common Error and Exception Enum end

    HOMEPAGE_NOT_FOUND(9601, "首页不存在！"),
    HOMEPAGE_ID_NOT_EXIST(9602, "ID不存在！"),
    HOMEPAGE_APP_EXIST(9603, "所选应用已有首页，请先删除！"),
    HOMEPAGE_APP_ID_NOT_EXIST(9604, "应用ID不存在！"),
    HOMEPAGE_USER_EXIST(9605, "所选人员已有首页，请先删除！"),
    HOMEPAGE_ORG_EXIST(9606, "所选机构已有首页，请先删除！"),
    HOMEPAGE_STATUS_EX(9607, "首页已发布，请先取消发布！"),
    HOMEPAGE_CN_NAME_EXIST(9608, "首页中文名称已存在"),
    HOMEPAGE_EN_NAME_EXIST(9609, "首页英文名称已存在"),
    ;

    /**
     * 返回码
     */
    private final int code;
    /**
     * 返回消息
     */
    private final String message;
}
