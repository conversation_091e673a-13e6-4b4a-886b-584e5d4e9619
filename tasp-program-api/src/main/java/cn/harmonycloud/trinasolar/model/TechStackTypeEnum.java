package cn.harmonycloud.trinasolar.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 技术栈类型枚举
 * 用于区分前端和后端应用
 */
@Getter
@AllArgsConstructor
public enum TechStackTypeEnum {
    FRONTEND("frontend", "前端"),
    BACKEND("backend", "后端");

    private final String code;
    private final String name;

    /**
     * 根据code获取枚举值
     */
    public static TechStackTypeEnum getByCode(String code) {
        for (TechStackTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
}