package cn.harmonycloud.trinasolar.model;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotEmpty;
@Data
@Accessors(chain = true)
public class AppCreateReqDTO {

    /**
     * 应用系统编码,自动生成
     */
    @NotEmpty(message = "应用系统编码不能为空")
    private String appCode;
    /**
     * 项目id
     */
    @NotEmpty(message = "项目空间id不能为空")
    private Long projectId;

    @NotEmpty(message = "管理git地址不能为空")
    private String gitUrl;

    @NotEmpty(message = "模板不能为空")
    private AppTemplate appTemplate;
}
