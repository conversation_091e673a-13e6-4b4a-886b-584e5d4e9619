package cn.harmonycloud.trinasolar.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "应用简略信息")
public class AppSystemVO {
    private static final long serialVersionUID = -4532784790141481074L;

    @Schema(description = "应用ID")
    private Long id;

    @Schema(description = "应用中文名称")
    private String cnName;

    @Schema(description = "应用英文名称")
    private String enName;

    @Schema(description= "业务负责人-userCode")
    private String businessLeader;

    @Schema(description= "技术负责人-userCode")
    private String technicalLeader;

    @Schema(description= "运维负责人-userCode")
    private String omLeader;

    @Schema(description= "系统负责人-userCode")
    private String systemLeader;
}
