package cn.harmonycloud.trinasolar.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/5/1
 */
@Data
@ApiModel(value = "User对象", description = "用户信息")
public class UserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("所属组织机构ID")
    private Long organizationId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("AD账号")
    private String username;

    @ApiModelProperty("统一身份账号")
    private String unityAccount;

    @ApiModelProperty("用户姓名")
    private String userRealname;

    @ApiModelProperty("用户头像")
    private String avatar;

    @ApiModelProperty("用户手机号")
    private String phone;

    @ApiModelProperty("用户邮箱")
    private String email;

    @ApiModelProperty("用户性别 0女 1男")
    private String sex;

    @ApiModelProperty("身份证号码")
    private String idNumber;

    @ApiModelProperty("工作电话")
    private String workMobile;

    @ApiModelProperty("家庭电话")
    private String homePhone;

    @ApiModelProperty("家庭住址")
    private String homeAddress;

    @ApiModelProperty("入职时间")
    private LocalDateTime entryTime;

    @ApiModelProperty("员工类型")
    private String staffType;

    @ApiModelProperty("职位级别代码")
    private String clevel;

    @ApiModelProperty("职务级别代码")
    private String plevel;

    @ApiModelProperty("职务名称代码")
    private String plevelName;

    @ApiModelProperty(value = "员工类型值")
    private String staffTypeValue;

    @ApiModelProperty("职位级别值")
    private String clevelValue;

    @ApiModelProperty("职务级别值")
    private String plevelValue;

    @ApiModelProperty("职务名称值")
    private String plevelNameValue;

    @ApiModelProperty("用户在职状态值")
    private String inServiceStatusValue;

    @ApiModelProperty("应用助手开通标识")
    private String appAssistantMark;

    @ApiModelProperty("借调组织机构ID")
    private Long secondOrgId;

    @ApiModelProperty("借调组织机构ID")
    private Long temporarilyTransferOrgId;

    @ApiModelProperty("兼职机构IDS")
    private String partTimeOrgIds;

    @ApiModelProperty("是否为管理员 0否 1是")
    private String admin;

    @ApiModelProperty("是否是同步的数据 0否 1是 2总部")
    private String syncFlag;

    @ApiModelProperty("用户在职状态代码")
    private String inServiceStatus;

    @ApiModelProperty("排序号")
    private Long sort;

    @ApiModelProperty("深信所属用户组")
    private String convinceGroup;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "锁定状态 0正常数据 1锁定数据")
    private Integer locked;

    @ApiModelProperty(value = "移动端设备序列号")
    private String serialNumber;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建人ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改人ID")
    private Long updaterId;

    @ApiModelProperty(value = "标记是否已分配平台角色 0未分配 1已分配")
    private String cloudRoleFlag;

    @ApiModelProperty(value = "标记是否已分配应用角色 0未分配 1已分配")
    private String appRoleFlag;

    @ApiModelProperty(value = "标记是否已分配应用数据角色 0未分配 1已分配")
    private String appDataRoleFlag;

    @ApiModelProperty(value = "所属组织机构id")
    private Long orgId;

    @ApiModelProperty(value = "所属组织机构编码")
    private String orgCode;

    @ApiModelProperty(value = "所属组织机构名称")
    private String orgName;

    @ApiModelProperty(value = "所属组织机构全路径名称")
    private String fullPathOrgName;

    @ApiModelProperty(value = "租户组")
    private String tenantGroup;

    @ApiModelProperty("所属一级组织机构ID")
    private Long oneOrgId;

    @ApiModelProperty("所属二级组织机构ID")
    private Long twoOrgId;

    @ApiModelProperty("所属三组织机构ID")
    private Long threeOrgId;
}
