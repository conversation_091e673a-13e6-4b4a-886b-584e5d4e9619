package cn.harmonycloud.trinasolar.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TechStackRankRespDTO {

    @ApiModelProperty(value = "应用程序总数")
    private Long applicationTotalCount;

    @ApiModelProperty(value = "前端应用数量")
    private Long frontendApplicationCount;

    @ApiModelProperty(value = "后端应用数量")
    private Long backendApplicationCount;

    @ApiModelProperty(value = "技术栈使用统计列表")
    private List<TechStackInfo> techStackUsageList;

    @Data
    public static class TechStackInfo {
        @ApiModelProperty(value = "技术栈标签")
        private String technicalStackTags;

        @ApiModelProperty(value = "使用数量")
        private Long count;
    }
}