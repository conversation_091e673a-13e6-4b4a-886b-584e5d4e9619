package cn.harmonycloud.trinasolar;

import cn.harmonycloud.trinasolar.model.ProgramDTO;
import cn.harmonycloud.trinasolar.model.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * <AUTHOR>
 */
@FeignClient(name = "harmonycloud-kepler-cloud-biz")
public interface CloudProvider {

    @GetMapping("/apps/{id}")
    R<ProgramDTO> getProgramById(@PathVariable("id") Long id);

}
