package cn.harmonycloud.trinasolar;

import cn.harmonycloud.trinasolar.model.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * <AUTHOR>
 */
@FeignClient(name = "harmonycloud-kepler-upms-biz", path = "/u/users")
public interface UpmsProvider {

    @GetMapping("/isAdmin/{appId}/{userId}")
    R<Boolean> isAdmin(@PathVariable("appId") Long appId, @PathVariable("userId") Long userId);
}
