package cn.harmonycloud.trinasolar;

import cn.harmonycloud.trinasolar.model.ProjectInfoDTO;
import cn.harmonycloud.trinasolar.model.R;
import cn.harmonycloud.trinasolar.model.User;
import cn.harmonycloud.trinasolar.model.UserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(name = "harmonycloud-kepler-upms-biz-gateway", path = "/harmony/kepler/upms/u/users", url = "${upms.url:tasp.trinasolar.com}")
public interface UpmsGatewayProvider {

    @GetMapping("/current")
    R<User> getCurrents(@RequestHeader("Authorization") String token);


    @PostMapping("/ids")
    R<List<UserVO>> getByIds(@RequestBody List<Long> ids);

    @GetMapping("/isAdmin/{appId}/{userId}")
    R<Boolean> isAdmin(@RequestHeader("Authorization") String token, @PathVariable("appId") Long appId, @PathVariable("userId") Long userId);

    @GetMapping("/apps/list/{userId}")
    R<List<ProjectInfoDTO>> getAppsInfo(@RequestHeader("Authorization") String token, @PathVariable("userId") Long userId);
}
