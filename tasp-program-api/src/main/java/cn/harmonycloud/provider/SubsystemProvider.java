package cn.harmonycloud.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.pojo.subsystem.MemberQuery;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import cn.harmonycloud.pojo.subsystem.SubsystemQueryDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import java.util.List;

@FeignClient(name = "subsystemFeign", path = "/provider/subSystem",url = "${development.url:http://devops-development-svc:8080}")
public interface SubsystemProvider {

    @ApiOperation("获取子系统成员")
    @GetMapping("/listMember")
    BaseResult<List<UserVo>> listMember(@SpringQueryMap MemberQuery memberQuery);

    @ApiOperation("子系统列表")
    @GetMapping("/list")
    BaseResult<List<SubsystemDto>> list(@SpringQueryMap SubsystemQueryDto queryDto);

}
