package cn.harmonycloud.util;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.exception.UnauthorizedException;
import cn.harmonycloud.constants.ApiConstance;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/17 4:45 下午
 **/
public class FeignUtil {

    /**
     * 校验code是否正确
     *
     * @param result
     * @param <T>
     * @return
     */
    public static <T> T getCheckCodeAndGetDate(BaseResult<T> result){
        if(result.getCode() == ApiConstance.OAUTH){
            throw new UnauthorizedException(result.getMsg());
        }
        if(result.getCode() != ApiConstance.OAUTH){
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, result.getMsg());
        }
        return result.getData();
    }
}
