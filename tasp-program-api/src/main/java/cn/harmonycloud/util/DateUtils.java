package cn.harmonycloud.util;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.pojo.TimeScope;
import cn.hutool.core.date.DateTime;
import org.springframework.boot.autoconfigure.web.format.DateTimeFormatters;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

public class DateUtils {

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String ZONE_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";

    /**
     * 获取当前时间
     */
    public static LocalDateTime getNowTime() {
        Date now = new Date();
        Instant instant = now.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * 将 yyyy-MM-dd 时间格式转化成 yyyyMMdd 字符串
     */
    public static String Date2String(DateTime time) {
        return time.toString().replaceAll("-", "");
    }

    public static String DateString2String(String time) {
        return time.replaceAll("-", "");
    }

    /**
     * 将 yyyyMMdd 时间格式转化成 yyyy-MM-dd 字符串
     */
    public static List<String> String2Date(String time) {
        String[] str = time.split("-");
        List<String> res = Arrays.asList(str);
        res.forEach((item) -> {
            String year = item.substring(0, 4);
            String month = item.substring(4, 6);
            String day = item.substring(6);
            item = year + "-" + month + "-" + day;
        });
        return res;
    }

    /**
     * 获取当前 yyyyMMdd 形式时间
     */
    public static String getNow(String format) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
        LocalDateTime now = LocalDateTime.now();
        return now.format(fmt);
    }

    /**
     * 获取时间范围
     * 0-今日、1-本周、2-本月
     */
    public static TimeScope getTimeScope(int type) {
        TimeScope timeScope = new TimeScope();
        LocalDate now = LocalDate.now();
        switch (type) {
            case 0:
                timeScope.setStartTime(LocalDateTime.of(now, LocalTime.MIN));
                timeScope.setEndTime(LocalDateTime.of(now, LocalTime.MAX));
                return timeScope;
            case 1:
                timeScope.setStartTime(LocalDateTime.of(now.with(DayOfWeek.MONDAY), LocalTime.MIN));
                timeScope.setEndTime(LocalDateTime.of(now.with(DayOfWeek.SUNDAY), LocalTime.MAX));
                return timeScope;
            case 2:
                timeScope.setStartTime(LocalDateTime.of(now.with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN));
                timeScope.setEndTime(LocalDateTime.of(now.with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX));
                return timeScope;
            default:
                return null;
        }
    }

    /**
     * 数据库获取时间格式转换
     */
    public static String getTime(Date time) {
        if (time == null) return null;
        return new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(time);
    }

    /**
     * localdatetime转字符串
     */
    public static String getTime(LocalDateTime time, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return time.format(formatter);
    }


    /**
     * 字符串转成localdatetime
     */
    public static LocalDateTime getTime(String date, String format) {
        if (StringUtils.isBlank(date) || StringUtils.isBlank(format)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        if ("yyyy-mm-dd".equals(format.toLowerCase(Locale.ROOT))) {
            return LocalDate.parse(date, formatter).atStartOfDay();
        }
        return LocalDateTime.parse(date, formatter);
    }


    /**
     * 字符串转成localdatetime
     */
    public static LocalDate getDateTime(String date, String format) {
        if (StringUtils.isBlank(date) || StringUtils.isBlank(format)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(date, formatter);
    }

    /**
     * Tue Jun 01 21:38:23 CST 2022 转 2022-06-01 21:38:23
     *
     * @param dateStr
     * @return
     */
    public static List<String> checkDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        if (!dateStr.contains("CST")) {
            return null;
        }
        SimpleDateFormat sdf2 = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
        Date data = null;
        try {
            data = sdf2.parse(dateStr);
            String formatDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(data);
            return Arrays.asList(formatDate.split(" "));

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getScopeString(LocalDateTime startTime, LocalDateTime endTime) {
        String startFormat = "yyyy-MM-dd HH:mm";
        String endFormat = "yyyy-MM-dd HH:mm";
        if (startTime.getYear() == endTime.getYear() && startTime.getDayOfYear() == endTime.getDayOfYear()) {
            endFormat = "HH:mm";
        }
        return getTime(startTime, startFormat) + "-" + getTime(endTime, endFormat);

    }


    /**
     * 转换时区
     *
     * @param sourceTime   待转换的时间，格式：  yyyy-MM-dd'T'HH:mm:ss.SSSxxx
     * @param targetFormat 目标格式
     * @param targetZoneId 目标时区
     * @return
     */
    public static String changeZoneTime(String sourceTime, String targetFormat, ZoneId targetZoneId) {
        DateTimeFormatter f = DateTimeFormatter.ofPattern(ZONE_DATE_FORMAT);
        LocalDateTime source = LocalDateTime.parse(sourceTime, f);
        String zone = sourceTime.substring(sourceTime.length() - 6);
        ZonedDateTime zonedDateTime = source.atZone(ZoneId.of(zone));
        DateTimeFormatter f2 = DateTimeFormatter.ofPattern(targetFormat).withZone(targetZoneId);
        return zonedDateTime.format(f2);
    }
}
