package cn.harmonycloud.util;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/5 6:55 下午
 **/
public class SystemUtils {

    final static String SUB_PREFIX = "SUBSYS_";

    /**
     * 子系统编码处理规则
     * @param code
     * @return
     */
    public static String getLowerSubSystemCode(String code){
        if(StringUtils.isEmpty(code)){
            return "";
        }
        if(code.startsWith(SUB_PREFIX)){
            return code.replace(SUB_PREFIX, "").toLowerCase().replaceAll("_", "");
        }
        return code.toLowerCase();
    }

    /**
     * 子系统编码处理规则
     * @param code
     * @return
     */
    public static String getRepositorySubSystemCode(String code){
        if(StringUtils.isEmpty(code)){
            return "";
        }
        if(code.startsWith(SUB_PREFIX)){
            return code.replace(SUB_PREFIX, "").toLowerCase();
        }
        return code.toLowerCase();
    }


    /**
     * 系统编码处理
     * @param code
     * @return
     */
    public static String getRepositorySystemCode(String code){
        if(StringUtils.isEmpty(code)){
            return "";
        }
        if(code.startsWith("SYS_")){
            return code.replace("SYS_", "").toLowerCase();
        }
        return code.toLowerCase();
    }
}
