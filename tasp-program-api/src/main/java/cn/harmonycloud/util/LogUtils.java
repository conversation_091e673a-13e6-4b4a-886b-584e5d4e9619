package cn.harmonycloud.util;

import lombok.extern.slf4j.Slf4j;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/25 7:10 下午
 **/
@Slf4j
public class LogUtils {

    public static String throwableExceptionString(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter, true);
        throwable.printStackTrace(printWriter);
        printWriter.close();
        return stringWriter.getBuffer().toString();
    }

    public static void log(Throwable throwable) {
        log.error(throwableExceptionString(throwable));
    }
}
