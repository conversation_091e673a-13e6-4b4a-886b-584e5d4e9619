package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;

@ApiModel("不同事项类型下根据状态阶段统计事项")
/* loaded from: StatisticsQuery.class */
public class StatisticsQuery {
    @ApiModelProperty("事项类型id")
    private List<Long> issuesTypeIdList;
    @ApiModelProperty("搜索条件")
    private Map<String, Object> search;

    public void setIssuesTypeIdList(final List<Long> issuesTypeIdList) {
        this.issuesTypeIdList = issuesTypeIdList;
    }

    public void setSearch(final Map<String, Object> search) {
        this.search = search;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof StatisticsQuery) {
            StatisticsQuery other = (StatisticsQuery) o;
            if (other.canEqual(this)) {
                Object this$issuesTypeIdList = getIssuesTypeIdList();
                Object other$issuesTypeIdList = other.getIssuesTypeIdList();
                if (this$issuesTypeIdList == null) {
                    if (other$issuesTypeIdList != null) {
                        return false;
                    }
                } else if (!this$issuesTypeIdList.equals(other$issuesTypeIdList)) {
                    return false;
                }
                Object this$search = getSearch();
                Object other$search = other.getSearch();
                return this$search == null ? other$search == null : this$search.equals(other$search);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof StatisticsQuery;
    }

    public int hashCode() {
        Object $issuesTypeIdList = getIssuesTypeIdList();
        int result = (1 * 59) + ($issuesTypeIdList == null ? 43 : $issuesTypeIdList.hashCode());
        Object $search = getSearch();
        return (result * 59) + ($search == null ? 43 : $search.hashCode());
    }

    public String toString() {
        return "StatisticsQuery(issuesTypeIdList=" + getIssuesTypeIdList() + ", search=" + getSearch() + ")";
    }

    public List<Long> getIssuesTypeIdList() {
        return this.issuesTypeIdList;
    }

    public Map<String, Object> getSearch() {
        return this.search;
    }
}
