package cn.harmonycloud.issue.model.dto.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel("查询项目接收参数")
/* loaded from: ProjectQueryDTO.class */
public class ProjectQueryDTO {
    @ApiModelProperty("项目名称")
    private String projectName;
    @ApiModelProperty("项目id")
    private List<Long> projectIdList;
    @ApiModelProperty("组织id")
    private String tenantId;

    public void setProjectName(final String projectName) {
        this.projectName = projectName;
    }

    public void setProjectIdList(final List<Long> projectIdList) {
        this.projectIdList = projectIdList;
    }

    public void setTenantId(final String tenantId) {
        this.tenantId = tenantId;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectQueryDTO) {
            ProjectQueryDTO other = (ProjectQueryDTO) o;
            if (other.canEqual(this)) {
                Object this$projectName = getProjectName();
                Object other$projectName = other.getProjectName();
                if (this$projectName == null) {
                    if (other$projectName != null) {
                        return false;
                    }
                } else if (!this$projectName.equals(other$projectName)) {
                    return false;
                }
                Object this$projectIdList = getProjectIdList();
                Object other$projectIdList = other.getProjectIdList();
                if (this$projectIdList == null) {
                    if (other$projectIdList != null) {
                        return false;
                    }
                } else if (!this$projectIdList.equals(other$projectIdList)) {
                    return false;
                }
                Object this$tenantId = getTenantId();
                Object other$tenantId = other.getTenantId();
                return this$tenantId == null ? other$tenantId == null : this$tenantId.equals(other$tenantId);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectQueryDTO;
    }

    public int hashCode() {
        Object $projectName = getProjectName();
        int result = (1 * 59) + ($projectName == null ? 43 : $projectName.hashCode());
        Object $projectIdList = getProjectIdList();
        int result2 = (result * 59) + ($projectIdList == null ? 43 : $projectIdList.hashCode());
        Object $tenantId = getTenantId();
        return (result2 * 59) + ($tenantId == null ? 43 : $tenantId.hashCode());
    }

    public String toString() {
        return "ProjectQueryDTO(projectName=" + getProjectName() + ", projectIdList=" + getProjectIdList() + ", tenantId=" + getTenantId() + ")";
    }

    public String getProjectName() {
        return this.projectName;
    }

    public List<Long> getProjectIdList() {
        return this.projectIdList;
    }

    public String getTenantId() {
        return this.tenantId;
    }
}
