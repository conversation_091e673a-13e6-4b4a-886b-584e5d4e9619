package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

@ApiModel("下拉列表值传输对象")
/* loaded from: ValueListDTO.class */
public class ValueListDTO implements Serializable {
    private static final long serialVersionUID = 1584431444909421341L;
    @ApiModelProperty("下拉列表值编码")
    private String code;
    @ApiModelProperty("下拉列表值名称")
    private String name;

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ValueListDTO) {
            ValueListDTO other = (ValueListDTO) o;
            if (other.canEqual(this)) {
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                return this$name == null ? other$name == null : this$name.equals(other$name);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ValueListDTO;
    }

    public int hashCode() {
        Object $code = getCode();
        int result = (1 * 59) + ($code == null ? 43 : $code.hashCode());
        Object $name = getName();
        return (result * 59) + ($name == null ? 43 : $name.hashCode());
    }

    public String toString() {
        return "ValueListDTO(code=" + getCode() + ", name=" + getName() + ")";
    }

    public ValueListDTO(final String code, final String name) {
        this.code = code;
        this.name = name;
    }

    public ValueListDTO() {
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
