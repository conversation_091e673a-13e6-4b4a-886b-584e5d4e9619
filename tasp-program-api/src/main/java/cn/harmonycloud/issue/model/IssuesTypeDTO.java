package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;

/* loaded from: IssuesTypeDTO.class */
public class IssuesTypeDTO implements Serializable {
    private static final long serialVersionUID = -4164319220946753151L;
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("事项类型编码")
    private String code;
    @ApiModelProperty("事项类型名称")
    private String name;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("图标")
    private String icon;
    @ApiModelProperty("默认状态")
    private Long defaultStatusId;
    @ApiModelProperty("是否系统状态")
    private Boolean isSystem;
    @ApiModelProperty("标准字段")
    private String standardField;
    @ApiModelProperty("功能插件")
    private String functionPlug;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public void setIcon(final String icon) {
        this.icon = icon;
    }

    public void setDefaultStatusId(final Long defaultStatusId) {
        this.defaultStatusId = defaultStatusId;
    }

    public void setIsSystem(final Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public void setStandardField(final String standardField) {
        this.standardField = standardField;
    }

    public void setFunctionPlug(final String functionPlug) {
        this.functionPlug = functionPlug;
    }

    public void setCreateTime(final LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public void setUpdateTime(final LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesTypeDTO) {
            IssuesTypeDTO other = (IssuesTypeDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$defaultStatusId = getDefaultStatusId();
                Object other$defaultStatusId = other.getDefaultStatusId();
                if (this$defaultStatusId == null) {
                    if (other$defaultStatusId != null) {
                        return false;
                    }
                } else if (!this$defaultStatusId.equals(other$defaultStatusId)) {
                    return false;
                }
                Object this$isSystem = getIsSystem();
                Object other$isSystem = other.getIsSystem();
                if (this$isSystem == null) {
                    if (other$isSystem != null) {
                        return false;
                    }
                } else if (!this$isSystem.equals(other$isSystem)) {
                    return false;
                }
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$description = getDescription();
                Object other$description = other.getDescription();
                if (this$description == null) {
                    if (other$description != null) {
                        return false;
                    }
                } else if (!this$description.equals(other$description)) {
                    return false;
                }
                Object this$icon = getIcon();
                Object other$icon = other.getIcon();
                if (this$icon == null) {
                    if (other$icon != null) {
                        return false;
                    }
                } else if (!this$icon.equals(other$icon)) {
                    return false;
                }
                Object this$standardField = getStandardField();
                Object other$standardField = other.getStandardField();
                if (this$standardField == null) {
                    if (other$standardField != null) {
                        return false;
                    }
                } else if (!this$standardField.equals(other$standardField)) {
                    return false;
                }
                Object this$functionPlug = getFunctionPlug();
                Object other$functionPlug = other.getFunctionPlug();
                if (this$functionPlug == null) {
                    if (other$functionPlug != null) {
                        return false;
                    }
                } else if (!this$functionPlug.equals(other$functionPlug)) {
                    return false;
                }
                Object this$createTime = getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }
                Object this$updateTime = getUpdateTime();
                Object other$updateTime = other.getUpdateTime();
                return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesTypeDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $defaultStatusId = getDefaultStatusId();
        int result2 = (result * 59) + ($defaultStatusId == null ? 43 : $defaultStatusId.hashCode());
        Object $isSystem = getIsSystem();
        int result3 = (result2 * 59) + ($isSystem == null ? 43 : $isSystem.hashCode());
        Object $code = getCode();
        int result4 = (result3 * 59) + ($code == null ? 43 : $code.hashCode());
        Object $name = getName();
        int result5 = (result4 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $description = getDescription();
        int result6 = (result5 * 59) + ($description == null ? 43 : $description.hashCode());
        Object $icon = getIcon();
        int result7 = (result6 * 59) + ($icon == null ? 43 : $icon.hashCode());
        Object $standardField = getStandardField();
        int result8 = (result7 * 59) + ($standardField == null ? 43 : $standardField.hashCode());
        Object $functionPlug = getFunctionPlug();
        int result9 = (result8 * 59) + ($functionPlug == null ? 43 : $functionPlug.hashCode());
        Object $createTime = getCreateTime();
        int result10 = (result9 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result10 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }

    public String toString() {
        return "IssuesTypeDTO(id=" + getId() + ", code=" + getCode() + ", name=" + getName() + ", description=" + getDescription() + ", icon=" + getIcon() + ", defaultStatusId=" + getDefaultStatusId() + ", isSystem=" + getIsSystem() + ", standardField=" + getStandardField() + ", functionPlug=" + getFunctionPlug() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public String getIcon() {
        return this.icon;
    }

    public Long getDefaultStatusId() {
        return this.defaultStatusId;
    }

    public Boolean getIsSystem() {
        return this.isSystem;
    }

    public String getStandardField() {
        return this.standardField;
    }

    public String getFunctionPlug() {
        return this.functionPlug;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
}
