package cn.harmonycloud.issue.model;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

/* loaded from: IssuesFieldDTO.class */
public class IssuesFieldDTO implements Serializable {
    private static final long serialVersionUID = 663805692627755525L;
    @ApiModelProperty("属性ID")
    private Long id;
    @ApiModelProperty("属性名称")
    private String name;
    @ApiModelProperty("属性编码")
    private String code;
    @ApiModelProperty("属性值")
    private String value;
    @ApiModelProperty("属性值名称")
    private String valueName;
    @ApiModelProperty("属性值选项")
    private List<ValueListDTO> values;
    @ApiModelProperty("树形结构列表")
    private List<Tree<Long>> valuesTree;
    @ApiModelProperty("系统类型")
    private Boolean isSystem;
    @ApiModelProperty(value = "控件类型", notes = "单行文本-shortText｜多行文本-longText|单选列表-singleList｜多选列表-multiList｜日期-date｜时间-time｜文件-file｜整数-number｜浮点数-decimal｜成员-person｜链接-link｜单选部门-singleTree｜多选部门-multiTree")
    private String fieldFormat;
    @ApiModelProperty("位置")
    private String position;
    @ApiModelProperty("默认值")
    private String defaultValue;
    @ApiModelProperty("是否创建时填写")
    private Boolean created;
    @ApiModelProperty("是否必填")
    private Boolean required;
    @ApiModelProperty("是否列表上展示")
    private Boolean viewList;
    @ApiModelProperty("是否作为搜索条件")
    private Boolean searchable;
    @ApiModelProperty("是否只读")
    private Boolean readOnly;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setValue(final String value) {
        this.value = value;
    }

    public void setValueName(final String valueName) {
        this.valueName = valueName;
    }

    public void setValues(final List<ValueListDTO> values) {
        this.values = values;
    }

    public void setValuesTree(final List<Tree<Long>> valuesTree) {
        this.valuesTree = valuesTree;
    }

    public void setIsSystem(final Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public void setFieldFormat(final String fieldFormat) {
        this.fieldFormat = fieldFormat;
    }

    public void setPosition(final String position) {
        this.position = position;
    }

    public void setDefaultValue(final String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public void setCreated(final Boolean created) {
        this.created = created;
    }

    public void setRequired(final Boolean required) {
        this.required = required;
    }

    public void setViewList(final Boolean viewList) {
        this.viewList = viewList;
    }

    public void setSearchable(final Boolean searchable) {
        this.searchable = searchable;
    }

    public void setReadOnly(final Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesFieldDTO) {
            IssuesFieldDTO other = (IssuesFieldDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$isSystem = getIsSystem();
                Object other$isSystem = other.getIsSystem();
                if (this$isSystem == null) {
                    if (other$isSystem != null) {
                        return false;
                    }
                } else if (!this$isSystem.equals(other$isSystem)) {
                    return false;
                }
                Object this$created = getCreated();
                Object other$created = other.getCreated();
                if (this$created == null) {
                    if (other$created != null) {
                        return false;
                    }
                } else if (!this$created.equals(other$created)) {
                    return false;
                }
                Object this$required = getRequired();
                Object other$required = other.getRequired();
                if (this$required == null) {
                    if (other$required != null) {
                        return false;
                    }
                } else if (!this$required.equals(other$required)) {
                    return false;
                }
                Object this$viewList = getViewList();
                Object other$viewList = other.getViewList();
                if (this$viewList == null) {
                    if (other$viewList != null) {
                        return false;
                    }
                } else if (!this$viewList.equals(other$viewList)) {
                    return false;
                }
                Object this$searchable = getSearchable();
                Object other$searchable = other.getSearchable();
                if (this$searchable == null) {
                    if (other$searchable != null) {
                        return false;
                    }
                } else if (!this$searchable.equals(other$searchable)) {
                    return false;
                }
                Object this$readOnly = getReadOnly();
                Object other$readOnly = other.getReadOnly();
                if (this$readOnly == null) {
                    if (other$readOnly != null) {
                        return false;
                    }
                } else if (!this$readOnly.equals(other$readOnly)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$value = getValue();
                Object other$value = other.getValue();
                if (this$value == null) {
                    if (other$value != null) {
                        return false;
                    }
                } else if (!this$value.equals(other$value)) {
                    return false;
                }
                Object this$valueName = getValueName();
                Object other$valueName = other.getValueName();
                if (this$valueName == null) {
                    if (other$valueName != null) {
                        return false;
                    }
                } else if (!this$valueName.equals(other$valueName)) {
                    return false;
                }
                Object this$values = getValues();
                Object other$values = other.getValues();
                if (this$values == null) {
                    if (other$values != null) {
                        return false;
                    }
                } else if (!this$values.equals(other$values)) {
                    return false;
                }
                Object this$valuesTree = getValuesTree();
                Object other$valuesTree = other.getValuesTree();
                if (this$valuesTree == null) {
                    if (other$valuesTree != null) {
                        return false;
                    }
                } else if (!this$valuesTree.equals(other$valuesTree)) {
                    return false;
                }
                Object this$fieldFormat = getFieldFormat();
                Object other$fieldFormat = other.getFieldFormat();
                if (this$fieldFormat == null) {
                    if (other$fieldFormat != null) {
                        return false;
                    }
                } else if (!this$fieldFormat.equals(other$fieldFormat)) {
                    return false;
                }
                Object this$position = getPosition();
                Object other$position = other.getPosition();
                if (this$position == null) {
                    if (other$position != null) {
                        return false;
                    }
                } else if (!this$position.equals(other$position)) {
                    return false;
                }
                Object this$defaultValue = getDefaultValue();
                Object other$defaultValue = other.getDefaultValue();
                return this$defaultValue == null ? other$defaultValue == null : this$defaultValue.equals(other$defaultValue);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesFieldDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $isSystem = getIsSystem();
        int result2 = (result * 59) + ($isSystem == null ? 43 : $isSystem.hashCode());
        Object $created = getCreated();
        int result3 = (result2 * 59) + ($created == null ? 43 : $created.hashCode());
        Object $required = getRequired();
        int result4 = (result3 * 59) + ($required == null ? 43 : $required.hashCode());
        Object $viewList = getViewList();
        int result5 = (result4 * 59) + ($viewList == null ? 43 : $viewList.hashCode());
        Object $searchable = getSearchable();
        int result6 = (result5 * 59) + ($searchable == null ? 43 : $searchable.hashCode());
        Object $readOnly = getReadOnly();
        int result7 = (result6 * 59) + ($readOnly == null ? 43 : $readOnly.hashCode());
        Object $name = getName();
        int result8 = (result7 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $code = getCode();
        int result9 = (result8 * 59) + ($code == null ? 43 : $code.hashCode());
        Object $value = getValue();
        int result10 = (result9 * 59) + ($value == null ? 43 : $value.hashCode());
        Object $valueName = getValueName();
        int result11 = (result10 * 59) + ($valueName == null ? 43 : $valueName.hashCode());
        Object $values = getValues();
        int result12 = (result11 * 59) + ($values == null ? 43 : $values.hashCode());
        Object $valuesTree = getValuesTree();
        int result13 = (result12 * 59) + ($valuesTree == null ? 43 : $valuesTree.hashCode());
        Object $fieldFormat = getFieldFormat();
        int result14 = (result13 * 59) + ($fieldFormat == null ? 43 : $fieldFormat.hashCode());
        Object $position = getPosition();
        int result15 = (result14 * 59) + ($position == null ? 43 : $position.hashCode());
        Object $defaultValue = getDefaultValue();
        return (result15 * 59) + ($defaultValue == null ? 43 : $defaultValue.hashCode());
    }

    public String toString() {
        return "IssuesFieldDTO(id=" + getId() + ", name=" + getName() + ", code=" + getCode() + ", value=" + getValue() + ", valueName=" + getValueName() + ", values=" + getValues() + ", valuesTree=" + getValuesTree() + ", isSystem=" + getIsSystem() + ", fieldFormat=" + getFieldFormat() + ", position=" + getPosition() + ", defaultValue=" + getDefaultValue() + ", created=" + getCreated() + ", required=" + getRequired() + ", viewList=" + getViewList() + ", searchable=" + getSearchable() + ", readOnly=" + getReadOnly() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public String getValueName() {
        return this.valueName;
    }

    public List<ValueListDTO> getValues() {
        return this.values;
    }

    public List<Tree<Long>> getValuesTree() {
        return this.valuesTree;
    }

    public Boolean getIsSystem() {
        return this.isSystem;
    }

    public String getFieldFormat() {
        return this.fieldFormat;
    }

    public String getPosition() {
        return this.position;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }

    public Boolean getCreated() {
        return this.created;
    }

    public Boolean getRequired() {
        return this.required;
    }

    public Boolean getViewList() {
        return this.viewList;
    }

    public Boolean getSearchable() {
        return this.searchable;
    }

    public Boolean getReadOnly() {
        return this.readOnly;
    }
}
