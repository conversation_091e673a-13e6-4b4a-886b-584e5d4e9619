package cn.harmonycloud.issue.model.vo.v2;

import io.swagger.annotations.ApiModelProperty;

/* loaded from: ProjectBaseVO.class */
public class ProjectBaseVO {
    @ApiModelProperty(name = "ID")
    private Long id;
    @ApiModelProperty(name = "项目code")
    private String projectCode;
    @ApiModelProperty(name = "项目名称")
    private String name;
    @ApiModelProperty(name = "租户id")
    private Long tenantId;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectCode(final String projectCode) {
        this.projectCode = projectCode;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setTenantId(final Long tenantId) {
        this.tenantId = tenantId;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectBaseVO) {
            ProjectBaseVO other = (ProjectBaseVO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$tenantId = getTenantId();
                Object other$tenantId = other.getTenantId();
                if (this$tenantId == null) {
                    if (other$tenantId != null) {
                        return false;
                    }
                } else if (!this$tenantId.equals(other$tenantId)) {
                    return false;
                }
                Object this$projectCode = getProjectCode();
                Object other$projectCode = other.getProjectCode();
                if (this$projectCode == null) {
                    if (other$projectCode != null) {
                        return false;
                    }
                } else if (!this$projectCode.equals(other$projectCode)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                return this$name == null ? other$name == null : this$name.equals(other$name);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectBaseVO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $tenantId = getTenantId();
        int result2 = (result * 59) + ($tenantId == null ? 43 : $tenantId.hashCode());
        Object $projectCode = getProjectCode();
        int result3 = (result2 * 59) + ($projectCode == null ? 43 : $projectCode.hashCode());
        Object $name = getName();
        return (result3 * 59) + ($name == null ? 43 : $name.hashCode());
    }

    public String toString() {
        return "ProjectBaseVO(id=" + getId() + ", projectCode=" + getProjectCode() + ", name=" + getName() + ", tenantId=" + getTenantId() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public String getProjectCode() {
        return this.projectCode;
    }

    public String getName() {
        return this.name;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ProjectBaseVO() {
    }

    public ProjectBaseVO(Long id, String projectCode, String name, Long tenantId) {
        this.id = id;
        this.projectCode = projectCode;
        this.name = name;
        this.tenantId = tenantId;
    }
}
