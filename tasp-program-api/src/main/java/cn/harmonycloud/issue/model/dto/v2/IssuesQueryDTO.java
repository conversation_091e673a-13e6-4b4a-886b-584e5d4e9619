package cn.harmonycloud.issue.model.dto.v2;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/* loaded from: IssuesQueryDTO.class */
public class IssuesQueryDTO {
    @ApiModelProperty("排除工作项ids")
    private List<Long> excludeIdList;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项类型：见TrackIssuesIssuesTypeEnum")
    private Long issuesTypeId;
    @ApiModelProperty("工作项ids")
    private List<Long> idList;
    @ApiModelProperty("项目ids")
    private List<Long> projectIdList;
    @ApiModelProperty("负责人ids")
    private List<Long> principalIdList;
    @ApiModelProperty("迭代id")
    private Long sprintId;
    @ApiModelProperty("版本id")
    private Long versionId;
    @ApiModelProperty("状态id")
    private Long statusId;
    @ApiModelProperty("优先级id")
    private Long priorityId;
    @ApiModelProperty("优先级名称")
    private String priority;
    @ApiModelProperty("当前页")
    private Integer pageNo = 1;
    @ApiModelProperty("分页单位")
    private Integer pageSize = 10;
    @ApiModelProperty("是否只查询基本信息(id+name)")
    private Boolean isBase = true;

    public void setPageNo(final Integer pageNo) {
        this.pageNo = pageNo;
    }

    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setExcludeIdList(final List<Long> excludeIdList) {
        this.excludeIdList = excludeIdList;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setIssuesTypeId(final Long issuesTypeId) {
        this.issuesTypeId = issuesTypeId;
    }

    public void setIdList(final List<Long> idList) {
        this.idList = idList;
    }

    public void setProjectIdList(final List<Long> projectIdList) {
        this.projectIdList = projectIdList;
    }

    public void setPrincipalIdList(final List<Long> principalIdList) {
        this.principalIdList = principalIdList;
    }

    public void setIsBase(final Boolean isBase) {
        this.isBase = isBase;
    }

    public void setSprintId(final Long sprintId) {
        this.sprintId = sprintId;
    }

    public void setVersionId(final Long versionId) {
        this.versionId = versionId;
    }

    public void setStatusId(final Long statusId) {
        this.statusId = statusId;
    }

    public void setPriorityId(final Long priorityId) {
        this.priorityId = priorityId;
    }

    public void setPriority(final String priority) {
        this.priority = priority;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesQueryDTO) {
            IssuesQueryDTO other = (IssuesQueryDTO) o;
            if (other.canEqual(this)) {
                Object this$pageNo = getPageNo();
                Object other$pageNo = other.getPageNo();
                if (this$pageNo == null) {
                    if (other$pageNo != null) {
                        return false;
                    }
                } else if (!this$pageNo.equals(other$pageNo)) {
                    return false;
                }
                Object this$pageSize = getPageSize();
                Object other$pageSize = other.getPageSize();
                if (this$pageSize == null) {
                    if (other$pageSize != null) {
                        return false;
                    }
                } else if (!this$pageSize.equals(other$pageSize)) {
                    return false;
                }
                Object this$issuesTypeId = getIssuesTypeId();
                Object other$issuesTypeId = other.getIssuesTypeId();
                if (this$issuesTypeId == null) {
                    if (other$issuesTypeId != null) {
                        return false;
                    }
                } else if (!this$issuesTypeId.equals(other$issuesTypeId)) {
                    return false;
                }
                Object this$isBase = getIsBase();
                Object other$isBase = other.getIsBase();
                if (this$isBase == null) {
                    if (other$isBase != null) {
                        return false;
                    }
                } else if (!this$isBase.equals(other$isBase)) {
                    return false;
                }
                Object this$sprintId = getSprintId();
                Object other$sprintId = other.getSprintId();
                if (this$sprintId == null) {
                    if (other$sprintId != null) {
                        return false;
                    }
                } else if (!this$sprintId.equals(other$sprintId)) {
                    return false;
                }
                Object this$versionId = getVersionId();
                Object other$versionId = other.getVersionId();
                if (this$versionId == null) {
                    if (other$versionId != null) {
                        return false;
                    }
                } else if (!this$versionId.equals(other$versionId)) {
                    return false;
                }
                Object this$statusId = getStatusId();
                Object other$statusId = other.getStatusId();
                if (this$statusId == null) {
                    if (other$statusId != null) {
                        return false;
                    }
                } else if (!this$statusId.equals(other$statusId)) {
                    return false;
                }
                Object this$priorityId = getPriorityId();
                Object other$priorityId = other.getPriorityId();
                if (this$priorityId == null) {
                    if (other$priorityId != null) {
                        return false;
                    }
                } else if (!this$priorityId.equals(other$priorityId)) {
                    return false;
                }
                Object this$excludeIdList = getExcludeIdList();
                Object other$excludeIdList = other.getExcludeIdList();
                if (this$excludeIdList == null) {
                    if (other$excludeIdList != null) {
                        return false;
                    }
                } else if (!this$excludeIdList.equals(other$excludeIdList)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$idList = getIdList();
                Object other$idList = other.getIdList();
                if (this$idList == null) {
                    if (other$idList != null) {
                        return false;
                    }
                } else if (!this$idList.equals(other$idList)) {
                    return false;
                }
                Object this$projectIdList = getProjectIdList();
                Object other$projectIdList = other.getProjectIdList();
                if (this$projectIdList == null) {
                    if (other$projectIdList != null) {
                        return false;
                    }
                } else if (!this$projectIdList.equals(other$projectIdList)) {
                    return false;
                }
                Object this$principalIdList = getPrincipalIdList();
                Object other$principalIdList = other.getPrincipalIdList();
                if (this$principalIdList == null) {
                    if (other$principalIdList != null) {
                        return false;
                    }
                } else if (!this$principalIdList.equals(other$principalIdList)) {
                    return false;
                }
                Object this$priority = getPriority();
                Object other$priority = other.getPriority();
                return this$priority == null ? other$priority == null : this$priority.equals(other$priority);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesQueryDTO;
    }

    public int hashCode() {
        Object $pageNo = getPageNo();
        int result = (1 * 59) + ($pageNo == null ? 43 : $pageNo.hashCode());
        Object $pageSize = getPageSize();
        int result2 = (result * 59) + ($pageSize == null ? 43 : $pageSize.hashCode());
        Object $issuesTypeId = getIssuesTypeId();
        int result3 = (result2 * 59) + ($issuesTypeId == null ? 43 : $issuesTypeId.hashCode());
        Object $isBase = getIsBase();
        int result4 = (result3 * 59) + ($isBase == null ? 43 : $isBase.hashCode());
        Object $sprintId = getSprintId();
        int result5 = (result4 * 59) + ($sprintId == null ? 43 : $sprintId.hashCode());
        Object $versionId = getVersionId();
        int result6 = (result5 * 59) + ($versionId == null ? 43 : $versionId.hashCode());
        Object $statusId = getStatusId();
        int result7 = (result6 * 59) + ($statusId == null ? 43 : $statusId.hashCode());
        Object $priorityId = getPriorityId();
        int result8 = (result7 * 59) + ($priorityId == null ? 43 : $priorityId.hashCode());
        Object $excludeIdList = getExcludeIdList();
        int result9 = (result8 * 59) + ($excludeIdList == null ? 43 : $excludeIdList.hashCode());
        Object $name = getName();
        int result10 = (result9 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $idList = getIdList();
        int result11 = (result10 * 59) + ($idList == null ? 43 : $idList.hashCode());
        Object $projectIdList = getProjectIdList();
        int result12 = (result11 * 59) + ($projectIdList == null ? 43 : $projectIdList.hashCode());
        Object $principalIdList = getPrincipalIdList();
        int result13 = (result12 * 59) + ($principalIdList == null ? 43 : $principalIdList.hashCode());
        Object $priority = getPriority();
        return (result13 * 59) + ($priority == null ? 43 : $priority.hashCode());
    }

    public String toString() {
        return "IssuesQueryDTO(pageNo=" + getPageNo() + ", pageSize=" + getPageSize() + ", excludeIdList=" + getExcludeIdList() + ", name=" + getName() + ", issuesTypeId=" + getIssuesTypeId() + ", idList=" + getIdList() + ", projectIdList=" + getProjectIdList() + ", principalIdList=" + getPrincipalIdList() + ", isBase=" + getIsBase() + ", sprintId=" + getSprintId() + ", versionId=" + getVersionId() + ", statusId=" + getStatusId() + ", priorityId=" + getPriorityId() + ", priority=" + getPriority() + ")";
    }

    public Integer getPageNo() {
        return this.pageNo;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public List<Long> getExcludeIdList() {
        return this.excludeIdList;
    }

    public String getName() {
        return this.name;
    }

    public Long getIssuesTypeId() {
        return this.issuesTypeId;
    }

    public List<Long> getIdList() {
        return this.idList;
    }

    public List<Long> getProjectIdList() {
        return this.projectIdList;
    }

    public List<Long> getPrincipalIdList() {
        return this.principalIdList;
    }

    public Boolean getIsBase() {
        return this.isBase;
    }

    public Long getSprintId() {
        return this.sprintId;
    }

    public Long getVersionId() {
        return this.versionId;
    }

    public Long getStatusId() {
        return this.statusId;
    }

    public Long getPriorityId() {
        return this.priorityId;
    }

    public String getPriority() {
        return this.priority;
    }
}
