package cn.harmonycloud.issue.model.vo.v2;

/* loaded from: BaseVO.class */
public class BaseVO {
    private Long id;
    private String code;
    private String name;
    private String description;
    private String url;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public void setUrl(final String url) {
        this.url = url;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof BaseVO) {
            BaseVO other = (BaseVO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$description = getDescription();
                Object other$description = other.getDescription();
                if (this$description == null) {
                    if (other$description != null) {
                        return false;
                    }
                } else if (!this$description.equals(other$description)) {
                    return false;
                }
                Object this$url = getUrl();
                Object other$url = other.getUrl();
                return this$url == null ? other$url == null : this$url.equals(other$url);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof BaseVO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $code = getCode();
        int result2 = (result * 59) + ($code == null ? 43 : $code.hashCode());
        Object $name = getName();
        int result3 = (result2 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $description = getDescription();
        int result4 = (result3 * 59) + ($description == null ? 43 : $description.hashCode());
        Object $url = getUrl();
        return (result4 * 59) + ($url == null ? 43 : $url.hashCode());
    }

    public String toString() {
        return "BaseVO(id=" + getId() + ", code=" + getCode() + ", name=" + getName() + ", description=" + getDescription() + ", url=" + getUrl() + ")";
    }

    public BaseVO(final Long id, final String code, final String name, final String description, final String url) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.description = description;
        this.url = url;
    }

    public BaseVO() {
    }

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public String getUrl() {
        return this.url;
    }

    public BaseVO(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public BaseVO(Long id, String name, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
    }

    public BaseVO codeAndName(Long id, String code, String name) {
        this.id = id;
        this.name = name;
        this.code = code;
        return this;
    }

    public BaseVO baseCode(Long id, String code) {
        this.id = id;
        this.code = code;
        return this;
    }

    public BaseVO(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
