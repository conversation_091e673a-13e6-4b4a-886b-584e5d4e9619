package cn.harmonycloud.issue.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import jakarta.validation.constraints.NotNull;

@JsonInclude(JsonInclude.Include.NON_NULL)
/* loaded from: WatchersDTO.class */
public class WatchersDTO implements Serializable {
    private static final long serialVersionUID = -4164319220946753151L;
    @NotNull
    @ApiModelProperty("事项id")
    private Long issuesId;
    @ApiModelProperty("用户id")
    private List<Long> userIdList;

    public void setIssuesId(final Long issuesId) {
        this.issuesId = issuesId;
    }

    public void setUserIdList(final List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof WatchersDTO) {
            WatchersDTO other = (WatchersDTO) o;
            if (other.canEqual(this)) {
                Object this$issuesId = getIssuesId();
                Object other$issuesId = other.getIssuesId();
                if (this$issuesId == null) {
                    if (other$issuesId != null) {
                        return false;
                    }
                } else if (!this$issuesId.equals(other$issuesId)) {
                    return false;
                }
                Object this$userIdList = getUserIdList();
                Object other$userIdList = other.getUserIdList();
                return this$userIdList == null ? other$userIdList == null : this$userIdList.equals(other$userIdList);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof WatchersDTO;
    }

    public int hashCode() {
        Object $issuesId = getIssuesId();
        int result = (1 * 59) + ($issuesId == null ? 43 : $issuesId.hashCode());
        Object $userIdList = getUserIdList();
        return (result * 59) + ($userIdList == null ? 43 : $userIdList.hashCode());
    }

    public String toString() {
        return "WatchersDTO(issuesId=" + getIssuesId() + ", userIdList=" + getUserIdList() + ")";
    }

    public Long getIssuesId() {
        return this.issuesId;
    }

    public List<Long> getUserIdList() {
        return this.userIdList;
    }
}
