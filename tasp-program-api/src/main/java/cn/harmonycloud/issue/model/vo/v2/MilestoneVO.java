package cn.harmonycloud.issue.model.vo.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

@ApiModel
/* loaded from: MilestoneVO.class */
public class MilestoneVO {
    @ApiModelProperty("节点信息")
    private List<MilestoneNodeVO> milestoneNodeVO;
    @ApiModelProperty("里程碑节点总数")
    private Integer totalNode;
    @ApiModelProperty("已完成里程碑节点数量")
    private Long doneNode;
    @ApiModelProperty("完成度")
    private BigDecimal doneRate;
    @ApiModelProperty("里程碑id")
    private Long id;
    private Integer readOnly;
    private String projectName;
    @ApiModelProperty("节点原信息")
    private List<MilestoneNodeVO> milestoneNode;

    public void setMilestoneNodeVO(final List<MilestoneNodeVO> milestoneNodeVO) {
        this.milestoneNodeVO = milestoneNodeVO;
    }

    public void setTotalNode(final Integer totalNode) {
        this.totalNode = totalNode;
    }

    public void setDoneNode(final Long doneNode) {
        this.doneNode = doneNode;
    }

    public void setDoneRate(final BigDecimal doneRate) {
        this.doneRate = doneRate;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public void setReadOnly(final Integer readOnly) {
        this.readOnly = readOnly;
    }

    public void setProjectName(final String projectName) {
        this.projectName = projectName;
    }

    public void setMilestoneNode(final List<MilestoneNodeVO> milestoneNode) {
        this.milestoneNode = milestoneNode;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof MilestoneVO) {
            MilestoneVO other = (MilestoneVO) o;
            if (other.canEqual(this)) {
                Object this$totalNode = getTotalNode();
                Object other$totalNode = other.getTotalNode();
                if (this$totalNode == null) {
                    if (other$totalNode != null) {
                        return false;
                    }
                } else if (!this$totalNode.equals(other$totalNode)) {
                    return false;
                }
                Object this$doneNode = getDoneNode();
                Object other$doneNode = other.getDoneNode();
                if (this$doneNode == null) {
                    if (other$doneNode != null) {
                        return false;
                    }
                } else if (!this$doneNode.equals(other$doneNode)) {
                    return false;
                }
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$readOnly = getReadOnly();
                Object other$readOnly = other.getReadOnly();
                if (this$readOnly == null) {
                    if (other$readOnly != null) {
                        return false;
                    }
                } else if (!this$readOnly.equals(other$readOnly)) {
                    return false;
                }
                Object this$milestoneNodeVO = getMilestoneNodeVO();
                Object other$milestoneNodeVO = other.getMilestoneNodeVO();
                if (this$milestoneNodeVO == null) {
                    if (other$milestoneNodeVO != null) {
                        return false;
                    }
                } else if (!this$milestoneNodeVO.equals(other$milestoneNodeVO)) {
                    return false;
                }
                Object this$doneRate = getDoneRate();
                Object other$doneRate = other.getDoneRate();
                if (this$doneRate == null) {
                    if (other$doneRate != null) {
                        return false;
                    }
                } else if (!this$doneRate.equals(other$doneRate)) {
                    return false;
                }
                Object this$projectName = getProjectName();
                Object other$projectName = other.getProjectName();
                if (this$projectName == null) {
                    if (other$projectName != null) {
                        return false;
                    }
                } else if (!this$projectName.equals(other$projectName)) {
                    return false;
                }
                Object this$milestoneNode = getMilestoneNode();
                Object other$milestoneNode = other.getMilestoneNode();
                return this$milestoneNode == null ? other$milestoneNode == null : this$milestoneNode.equals(other$milestoneNode);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof MilestoneVO;
    }

    public int hashCode() {
        Object $totalNode = getTotalNode();
        int result = (1 * 59) + ($totalNode == null ? 43 : $totalNode.hashCode());
        Object $doneNode = getDoneNode();
        int result2 = (result * 59) + ($doneNode == null ? 43 : $doneNode.hashCode());
        Object $id = getId();
        int result3 = (result2 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $readOnly = getReadOnly();
        int result4 = (result3 * 59) + ($readOnly == null ? 43 : $readOnly.hashCode());
        Object $milestoneNodeVO = getMilestoneNodeVO();
        int result5 = (result4 * 59) + ($milestoneNodeVO == null ? 43 : $milestoneNodeVO.hashCode());
        Object $doneRate = getDoneRate();
        int result6 = (result5 * 59) + ($doneRate == null ? 43 : $doneRate.hashCode());
        Object $projectName = getProjectName();
        int result7 = (result6 * 59) + ($projectName == null ? 43 : $projectName.hashCode());
        Object $milestoneNode = getMilestoneNode();
        return (result7 * 59) + ($milestoneNode == null ? 43 : $milestoneNode.hashCode());
    }

    public String toString() {
        return "MilestoneVO(milestoneNodeVO=" + getMilestoneNodeVO() + ", totalNode=" + getTotalNode() + ", doneNode=" + getDoneNode() + ", doneRate=" + getDoneRate() + ", id=" + getId() + ", readOnly=" + getReadOnly() + ", projectName=" + getProjectName() + ", milestoneNode=" + getMilestoneNode() + ")";
    }

    public List<MilestoneNodeVO> getMilestoneNodeVO() {
        return this.milestoneNodeVO;
    }

    public Integer getTotalNode() {
        return this.totalNode;
    }

    public Long getDoneNode() {
        return this.doneNode;
    }

    public BigDecimal getDoneRate() {
        return this.doneRate;
    }

    public Long getId() {
        return this.id;
    }

    public Integer getReadOnly() {
        return this.readOnly;
    }

    public String getProjectName() {
        return this.projectName;
    }

    public List<MilestoneNodeVO> getMilestoneNode() {
        return this.milestoneNode;
    }
}
