package cn.harmonycloud.issue.model.dto.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class IssuesDTO {
    @ApiModelProperty("工作项id")
    private Long id;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("工作项编号")
    private String code;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项类型id")
    private Long issuesClassicId;
    @ApiModelProperty("工作项类型名称")
    private String issuesClassicName;
    @ApiModelProperty("状态名称")
    private String status;
    @ApiModelProperty("迭代名称")
    private String sprint;
    @ApiModelProperty("负责人")
    private String assignedName;
    @ApiModelProperty("负责人")
    private List<UserDTO> ownerList;
    @ApiModelProperty("优先级名称")
    private String priorityName;
    @ApiModelProperty("优先级id")
    private Long priorityId;
    @ApiModelProperty("状态id")
    private Long statusId;
    @ApiModelProperty("迭代id")
    private Long sprintId;
    @ApiModelProperty("版本id")
    private String versionId;
    @ApiModelProperty("版本名称")
    private String version;
}
