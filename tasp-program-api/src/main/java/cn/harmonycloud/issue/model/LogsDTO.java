package cn.harmonycloud.issue.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
/* loaded from: LogsDTO.class */
public class LogsDTO implements Serializable {
    private static final long serialVersionUID = -4164319220946753151L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField("issues_id")
    private Long issuesId;
    @ApiModelProperty("行为(创建/修改/关联/状态流转)")
    private String action;
    @ApiModelProperty("属性ID")
    private Long propertyId;
    @ApiModelProperty("被更新的属性名称")
    private String propertyKey;
    @ApiModelProperty("属性值更新之前关联id,用于跳转")
    private String propertyValueIdBefore;
    @ApiModelProperty("属性值更新之前")
    private String propertyValueNameBefore;
    @ApiModelProperty("属性值更新之后关联id,用于跳转")
    private String propertyValueIdAfter;
    @ApiModelProperty("属性值更新之后")
    private String propertyValueNameAfter;
    @ApiModelProperty("创建用户id")
    private Long createBy;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setIssuesId(final Long issuesId) {
        this.issuesId = issuesId;
    }

    public void setAction(final String action) {
        this.action = action;
    }

    public void setPropertyId(final Long propertyId) {
        this.propertyId = propertyId;
    }

    public void setPropertyKey(final String propertyKey) {
        this.propertyKey = propertyKey;
    }

    public void setPropertyValueIdBefore(final String propertyValueIdBefore) {
        this.propertyValueIdBefore = propertyValueIdBefore;
    }

    public void setPropertyValueNameBefore(final String propertyValueNameBefore) {
        this.propertyValueNameBefore = propertyValueNameBefore;
    }

    public void setPropertyValueIdAfter(final String propertyValueIdAfter) {
        this.propertyValueIdAfter = propertyValueIdAfter;
    }

    public void setPropertyValueNameAfter(final String propertyValueNameAfter) {
        this.propertyValueNameAfter = propertyValueNameAfter;
    }

    public void setCreateBy(final Long createBy) {
        this.createBy = createBy;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof LogsDTO) {
            LogsDTO other = (LogsDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$issuesId = getIssuesId();
                Object other$issuesId = other.getIssuesId();
                if (this$issuesId == null) {
                    if (other$issuesId != null) {
                        return false;
                    }
                } else if (!this$issuesId.equals(other$issuesId)) {
                    return false;
                }
                Object this$propertyId = getPropertyId();
                Object other$propertyId = other.getPropertyId();
                if (this$propertyId == null) {
                    if (other$propertyId != null) {
                        return false;
                    }
                } else if (!this$propertyId.equals(other$propertyId)) {
                    return false;
                }
                Object this$createBy = getCreateBy();
                Object other$createBy = other.getCreateBy();
                if (this$createBy == null) {
                    if (other$createBy != null) {
                        return false;
                    }
                } else if (!this$createBy.equals(other$createBy)) {
                    return false;
                }
                Object this$action = getAction();
                Object other$action = other.getAction();
                if (this$action == null) {
                    if (other$action != null) {
                        return false;
                    }
                } else if (!this$action.equals(other$action)) {
                    return false;
                }
                Object this$propertyKey = getPropertyKey();
                Object other$propertyKey = other.getPropertyKey();
                if (this$propertyKey == null) {
                    if (other$propertyKey != null) {
                        return false;
                    }
                } else if (!this$propertyKey.equals(other$propertyKey)) {
                    return false;
                }
                Object this$propertyValueIdBefore = getPropertyValueIdBefore();
                Object other$propertyValueIdBefore = other.getPropertyValueIdBefore();
                if (this$propertyValueIdBefore == null) {
                    if (other$propertyValueIdBefore != null) {
                        return false;
                    }
                } else if (!this$propertyValueIdBefore.equals(other$propertyValueIdBefore)) {
                    return false;
                }
                Object this$propertyValueNameBefore = getPropertyValueNameBefore();
                Object other$propertyValueNameBefore = other.getPropertyValueNameBefore();
                if (this$propertyValueNameBefore == null) {
                    if (other$propertyValueNameBefore != null) {
                        return false;
                    }
                } else if (!this$propertyValueNameBefore.equals(other$propertyValueNameBefore)) {
                    return false;
                }
                Object this$propertyValueIdAfter = getPropertyValueIdAfter();
                Object other$propertyValueIdAfter = other.getPropertyValueIdAfter();
                if (this$propertyValueIdAfter == null) {
                    if (other$propertyValueIdAfter != null) {
                        return false;
                    }
                } else if (!this$propertyValueIdAfter.equals(other$propertyValueIdAfter)) {
                    return false;
                }
                Object this$propertyValueNameAfter = getPropertyValueNameAfter();
                Object other$propertyValueNameAfter = other.getPropertyValueNameAfter();
                return this$propertyValueNameAfter == null ? other$propertyValueNameAfter == null : this$propertyValueNameAfter.equals(other$propertyValueNameAfter);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof LogsDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $issuesId = getIssuesId();
        int result2 = (result * 59) + ($issuesId == null ? 43 : $issuesId.hashCode());
        Object $propertyId = getPropertyId();
        int result3 = (result2 * 59) + ($propertyId == null ? 43 : $propertyId.hashCode());
        Object $createBy = getCreateBy();
        int result4 = (result3 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $action = getAction();
        int result5 = (result4 * 59) + ($action == null ? 43 : $action.hashCode());
        Object $propertyKey = getPropertyKey();
        int result6 = (result5 * 59) + ($propertyKey == null ? 43 : $propertyKey.hashCode());
        Object $propertyValueIdBefore = getPropertyValueIdBefore();
        int result7 = (result6 * 59) + ($propertyValueIdBefore == null ? 43 : $propertyValueIdBefore.hashCode());
        Object $propertyValueNameBefore = getPropertyValueNameBefore();
        int result8 = (result7 * 59) + ($propertyValueNameBefore == null ? 43 : $propertyValueNameBefore.hashCode());
        Object $propertyValueIdAfter = getPropertyValueIdAfter();
        int result9 = (result8 * 59) + ($propertyValueIdAfter == null ? 43 : $propertyValueIdAfter.hashCode());
        Object $propertyValueNameAfter = getPropertyValueNameAfter();
        return (result9 * 59) + ($propertyValueNameAfter == null ? 43 : $propertyValueNameAfter.hashCode());
    }

    public String toString() {
        return "LogsDTO(id=" + getId() + ", issuesId=" + getIssuesId() + ", action=" + getAction() + ", propertyId=" + getPropertyId() + ", propertyKey=" + getPropertyKey() + ", propertyValueIdBefore=" + getPropertyValueIdBefore() + ", propertyValueNameBefore=" + getPropertyValueNameBefore() + ", propertyValueIdAfter=" + getPropertyValueIdAfter() + ", propertyValueNameAfter=" + getPropertyValueNameAfter() + ", createBy=" + getCreateBy() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getIssuesId() {
        return this.issuesId;
    }

    public String getAction() {
        return this.action;
    }

    public Long getPropertyId() {
        return this.propertyId;
    }

    public String getPropertyKey() {
        return this.propertyKey;
    }

    public String getPropertyValueIdBefore() {
        return this.propertyValueIdBefore;
    }

    public String getPropertyValueNameBefore() {
        return this.propertyValueNameBefore;
    }

    public String getPropertyValueIdAfter() {
        return this.propertyValueIdAfter;
    }

    public String getPropertyValueNameAfter() {
        return this.propertyValueNameAfter;
    }

    public Long getCreateBy() {
        return this.createBy;
    }
}
