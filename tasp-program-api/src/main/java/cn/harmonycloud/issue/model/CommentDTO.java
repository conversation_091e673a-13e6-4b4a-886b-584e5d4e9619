package cn.harmonycloud.issue.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
/* loaded from: CommentDTO.class */
public class CommentDTO implements Serializable {
    private static final long serialVersionUID = -4164319220946753151L;
    @ApiModelProperty("评论id")
    private Long id;
    @ApiModelProperty("事项id")
    private Long issuesId;
    @ApiModelProperty("作者id")
    private Long authId;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty("父级评论id")
    private Long parentId;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("创建人")
    private Long createBy;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setIssuesId(final Long issuesId) {
        this.issuesId = issuesId;
    }

    public void setAuthId(final Long authId) {
        this.authId = authId;
    }

    public void setContent(final String content) {
        this.content = content;
    }

    public void setParentId(final Long parentId) {
        this.parentId = parentId;
    }

    public void setCreateTime(final LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public void setCreateBy(final Long createBy) {
        this.createBy = createBy;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof CommentDTO) {
            CommentDTO other = (CommentDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$issuesId = getIssuesId();
                Object other$issuesId = other.getIssuesId();
                if (this$issuesId == null) {
                    if (other$issuesId != null) {
                        return false;
                    }
                } else if (!this$issuesId.equals(other$issuesId)) {
                    return false;
                }
                Object this$authId = getAuthId();
                Object other$authId = other.getAuthId();
                if (this$authId == null) {
                    if (other$authId != null) {
                        return false;
                    }
                } else if (!this$authId.equals(other$authId)) {
                    return false;
                }
                Object this$parentId = getParentId();
                Object other$parentId = other.getParentId();
                if (this$parentId == null) {
                    if (other$parentId != null) {
                        return false;
                    }
                } else if (!this$parentId.equals(other$parentId)) {
                    return false;
                }
                Object this$createBy = getCreateBy();
                Object other$createBy = other.getCreateBy();
                if (this$createBy == null) {
                    if (other$createBy != null) {
                        return false;
                    }
                } else if (!this$createBy.equals(other$createBy)) {
                    return false;
                }
                Object this$content = getContent();
                Object other$content = other.getContent();
                if (this$content == null) {
                    if (other$content != null) {
                        return false;
                    }
                } else if (!this$content.equals(other$content)) {
                    return false;
                }
                Object this$createTime = getCreateTime();
                Object other$createTime = other.getCreateTime();
                return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof CommentDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $issuesId = getIssuesId();
        int result2 = (result * 59) + ($issuesId == null ? 43 : $issuesId.hashCode());
        Object $authId = getAuthId();
        int result3 = (result2 * 59) + ($authId == null ? 43 : $authId.hashCode());
        Object $parentId = getParentId();
        int result4 = (result3 * 59) + ($parentId == null ? 43 : $parentId.hashCode());
        Object $createBy = getCreateBy();
        int result5 = (result4 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $content = getContent();
        int result6 = (result5 * 59) + ($content == null ? 43 : $content.hashCode());
        Object $createTime = getCreateTime();
        return (result6 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }

    public String toString() {
        return "CommentDTO(id=" + getId() + ", issuesId=" + getIssuesId() + ", authId=" + getAuthId() + ", content=" + getContent() + ", parentId=" + getParentId() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getIssuesId() {
        return this.issuesId;
    }

    public Long getAuthId() {
        return this.authId;
    }

    public String getContent() {
        return this.content;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public Long getCreateBy() {
        return this.createBy;
    }
}
