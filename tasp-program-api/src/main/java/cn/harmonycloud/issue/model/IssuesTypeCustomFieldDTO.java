package cn.harmonycloud.issue.model;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

/* loaded from: IssuesTypeCustomFieldDTO.class */
public class IssuesTypeCustomFieldDTO implements Serializable {
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("事项类型ID")
    private Long issuesTypeId;
    @ApiModelProperty("属性ID")
    private Long customFieldId;
    @ApiModelProperty("属性名称")
    private String name;
    @ApiModelProperty("属性编码")
    private String code;
    @ApiModelProperty("属性值")
    private String value;
    @ApiModelProperty(value = "属性值选项列表", notes = "默认为空集合，前端要求")
    private List<ValueListDTO> values;
    @ApiModelProperty("树形结构列表")
    private List<Tree<Long>> valuesTree;
    @ApiModelProperty(value = "选项来源", notes = "列表：1-自定义｜2-数据字典｜3-远程接口；树：1 部门 2 静态树 3 远程接口")
    private Integer optionSource;
    @ApiModelProperty("接口地址")
    private String interfaceUrl;
    @ApiModelProperty("是否支持模糊查询")
    private Boolean likeSearch;
    @ApiModelProperty("系统类型")
    private Boolean isSystem;
    @ApiModelProperty(value = "控件类型", notes = "单行文本-shortText｜多行文本-longText｜单选列表-singleList｜多选列表-multiList｜日期-date｜时间-time｜文件-file｜整数-number｜浮点数-decimal｜成员-person｜链接-link｜单选部门-singleTree｜多选部门-multiTree")
    private String fieldFormat;
    @ApiModelProperty("位置")
    private Integer position;
    @ApiModelProperty("默认值")
    private String defaultValue;
    @ApiModelProperty("是否创建时填写")
    private Boolean created;
    @ApiModelProperty("是否必填")
    private Boolean required;
    @ApiModelProperty("数据来源：1-用户|2-部门|3-项目|4-系统")
    private Integer sourceType;
    @ApiModelProperty("角色ids")
    private String roleIds;
    @ApiModelProperty("标签")
    private String tag;
    @ApiModelProperty("允许搜索的字段")
    private Boolean searchable;
    @ApiModelProperty("是否修改时的字段")
    private Boolean updated;
    @ApiModelProperty("事项id")
    private Long issuesId;
    @ApiModelProperty("是否默认列表上展示")
    private Boolean viewList = false;
    @ApiModelProperty("字段权限")
    private String permission = "read_and_write";

    public void setId(final Long id) {
        this.id = id;
    }

    public void setIssuesTypeId(final Long issuesTypeId) {
        this.issuesTypeId = issuesTypeId;
    }

    public void setCustomFieldId(final Long customFieldId) {
        this.customFieldId = customFieldId;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setValue(final String value) {
        this.value = value;
    }

    public void setValues(final List<ValueListDTO> values) {
        this.values = values;
    }

    public void setValuesTree(final List<Tree<Long>> valuesTree) {
        this.valuesTree = valuesTree;
    }

    public void setOptionSource(final Integer optionSource) {
        this.optionSource = optionSource;
    }

    public void setInterfaceUrl(final String interfaceUrl) {
        this.interfaceUrl = interfaceUrl;
    }

    public void setLikeSearch(final Boolean likeSearch) {
        this.likeSearch = likeSearch;
    }

    public void setIsSystem(final Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public void setFieldFormat(final String fieldFormat) {
        this.fieldFormat = fieldFormat;
    }

    public void setPosition(final Integer position) {
        this.position = position;
    }

    public void setDefaultValue(final String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public void setCreated(final Boolean created) {
        this.created = created;
    }

    public void setRequired(final Boolean required) {
        this.required = required;
    }

    public void setSourceType(final Integer sourceType) {
        this.sourceType = sourceType;
    }

    public void setViewList(final Boolean viewList) {
        this.viewList = viewList;
    }

    public void setRoleIds(final String roleIds) {
        this.roleIds = roleIds;
    }

    public void setPermission(final String permission) {
        this.permission = permission;
    }

    public void setTag(final String tag) {
        this.tag = tag;
    }

    public void setSearchable(final Boolean searchable) {
        this.searchable = searchable;
    }

    public void setUpdated(final Boolean updated) {
        this.updated = updated;
    }

    public void setIssuesId(final Long issuesId) {
        this.issuesId = issuesId;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesTypeCustomFieldDTO) {
            IssuesTypeCustomFieldDTO other = (IssuesTypeCustomFieldDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$issuesTypeId = getIssuesTypeId();
                Object other$issuesTypeId = other.getIssuesTypeId();
                if (this$issuesTypeId == null) {
                    if (other$issuesTypeId != null) {
                        return false;
                    }
                } else if (!this$issuesTypeId.equals(other$issuesTypeId)) {
                    return false;
                }
                Object this$customFieldId = getCustomFieldId();
                Object other$customFieldId = other.getCustomFieldId();
                if (this$customFieldId == null) {
                    if (other$customFieldId != null) {
                        return false;
                    }
                } else if (!this$customFieldId.equals(other$customFieldId)) {
                    return false;
                }
                Object this$optionSource = getOptionSource();
                Object other$optionSource = other.getOptionSource();
                if (this$optionSource == null) {
                    if (other$optionSource != null) {
                        return false;
                    }
                } else if (!this$optionSource.equals(other$optionSource)) {
                    return false;
                }
                Object this$likeSearch = getLikeSearch();
                Object other$likeSearch = other.getLikeSearch();
                if (this$likeSearch == null) {
                    if (other$likeSearch != null) {
                        return false;
                    }
                } else if (!this$likeSearch.equals(other$likeSearch)) {
                    return false;
                }
                Object this$isSystem = getIsSystem();
                Object other$isSystem = other.getIsSystem();
                if (this$isSystem == null) {
                    if (other$isSystem != null) {
                        return false;
                    }
                } else if (!this$isSystem.equals(other$isSystem)) {
                    return false;
                }
                Object this$position = getPosition();
                Object other$position = other.getPosition();
                if (this$position == null) {
                    if (other$position != null) {
                        return false;
                    }
                } else if (!this$position.equals(other$position)) {
                    return false;
                }
                Object this$created = getCreated();
                Object other$created = other.getCreated();
                if (this$created == null) {
                    if (other$created != null) {
                        return false;
                    }
                } else if (!this$created.equals(other$created)) {
                    return false;
                }
                Object this$required = getRequired();
                Object other$required = other.getRequired();
                if (this$required == null) {
                    if (other$required != null) {
                        return false;
                    }
                } else if (!this$required.equals(other$required)) {
                    return false;
                }
                Object this$sourceType = getSourceType();
                Object other$sourceType = other.getSourceType();
                if (this$sourceType == null) {
                    if (other$sourceType != null) {
                        return false;
                    }
                } else if (!this$sourceType.equals(other$sourceType)) {
                    return false;
                }
                Object this$viewList = getViewList();
                Object other$viewList = other.getViewList();
                if (this$viewList == null) {
                    if (other$viewList != null) {
                        return false;
                    }
                } else if (!this$viewList.equals(other$viewList)) {
                    return false;
                }
                Object this$searchable = getSearchable();
                Object other$searchable = other.getSearchable();
                if (this$searchable == null) {
                    if (other$searchable != null) {
                        return false;
                    }
                } else if (!this$searchable.equals(other$searchable)) {
                    return false;
                }
                Object this$updated = getUpdated();
                Object other$updated = other.getUpdated();
                if (this$updated == null) {
                    if (other$updated != null) {
                        return false;
                    }
                } else if (!this$updated.equals(other$updated)) {
                    return false;
                }
                Object this$issuesId = getIssuesId();
                Object other$issuesId = other.getIssuesId();
                if (this$issuesId == null) {
                    if (other$issuesId != null) {
                        return false;
                    }
                } else if (!this$issuesId.equals(other$issuesId)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$value = getValue();
                Object other$value = other.getValue();
                if (this$value == null) {
                    if (other$value != null) {
                        return false;
                    }
                } else if (!this$value.equals(other$value)) {
                    return false;
                }
                Object this$values = getValues();
                Object other$values = other.getValues();
                if (this$values == null) {
                    if (other$values != null) {
                        return false;
                    }
                } else if (!this$values.equals(other$values)) {
                    return false;
                }
                Object this$valuesTree = getValuesTree();
                Object other$valuesTree = other.getValuesTree();
                if (this$valuesTree == null) {
                    if (other$valuesTree != null) {
                        return false;
                    }
                } else if (!this$valuesTree.equals(other$valuesTree)) {
                    return false;
                }
                Object this$interfaceUrl = getInterfaceUrl();
                Object other$interfaceUrl = other.getInterfaceUrl();
                if (this$interfaceUrl == null) {
                    if (other$interfaceUrl != null) {
                        return false;
                    }
                } else if (!this$interfaceUrl.equals(other$interfaceUrl)) {
                    return false;
                }
                Object this$fieldFormat = getFieldFormat();
                Object other$fieldFormat = other.getFieldFormat();
                if (this$fieldFormat == null) {
                    if (other$fieldFormat != null) {
                        return false;
                    }
                } else if (!this$fieldFormat.equals(other$fieldFormat)) {
                    return false;
                }
                Object this$defaultValue = getDefaultValue();
                Object other$defaultValue = other.getDefaultValue();
                if (this$defaultValue == null) {
                    if (other$defaultValue != null) {
                        return false;
                    }
                } else if (!this$defaultValue.equals(other$defaultValue)) {
                    return false;
                }
                Object this$roleIds = getRoleIds();
                Object other$roleIds = other.getRoleIds();
                if (this$roleIds == null) {
                    if (other$roleIds != null) {
                        return false;
                    }
                } else if (!this$roleIds.equals(other$roleIds)) {
                    return false;
                }
                Object this$permission = getPermission();
                Object other$permission = other.getPermission();
                if (this$permission == null) {
                    if (other$permission != null) {
                        return false;
                    }
                } else if (!this$permission.equals(other$permission)) {
                    return false;
                }
                Object this$tag = getTag();
                Object other$tag = other.getTag();
                return this$tag == null ? other$tag == null : this$tag.equals(other$tag);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesTypeCustomFieldDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $issuesTypeId = getIssuesTypeId();
        int result2 = (result * 59) + ($issuesTypeId == null ? 43 : $issuesTypeId.hashCode());
        Object $customFieldId = getCustomFieldId();
        int result3 = (result2 * 59) + ($customFieldId == null ? 43 : $customFieldId.hashCode());
        Object $optionSource = getOptionSource();
        int result4 = (result3 * 59) + ($optionSource == null ? 43 : $optionSource.hashCode());
        Object $likeSearch = getLikeSearch();
        int result5 = (result4 * 59) + ($likeSearch == null ? 43 : $likeSearch.hashCode());
        Object $isSystem = getIsSystem();
        int result6 = (result5 * 59) + ($isSystem == null ? 43 : $isSystem.hashCode());
        Object $position = getPosition();
        int result7 = (result6 * 59) + ($position == null ? 43 : $position.hashCode());
        Object $created = getCreated();
        int result8 = (result7 * 59) + ($created == null ? 43 : $created.hashCode());
        Object $required = getRequired();
        int result9 = (result8 * 59) + ($required == null ? 43 : $required.hashCode());
        Object $sourceType = getSourceType();
        int result10 = (result9 * 59) + ($sourceType == null ? 43 : $sourceType.hashCode());
        Object $viewList = getViewList();
        int result11 = (result10 * 59) + ($viewList == null ? 43 : $viewList.hashCode());
        Object $searchable = getSearchable();
        int result12 = (result11 * 59) + ($searchable == null ? 43 : $searchable.hashCode());
        Object $updated = getUpdated();
        int result13 = (result12 * 59) + ($updated == null ? 43 : $updated.hashCode());
        Object $issuesId = getIssuesId();
        int result14 = (result13 * 59) + ($issuesId == null ? 43 : $issuesId.hashCode());
        Object $name = getName();
        int result15 = (result14 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $code = getCode();
        int result16 = (result15 * 59) + ($code == null ? 43 : $code.hashCode());
        Object $value = getValue();
        int result17 = (result16 * 59) + ($value == null ? 43 : $value.hashCode());
        Object $values = getValues();
        int result18 = (result17 * 59) + ($values == null ? 43 : $values.hashCode());
        Object $valuesTree = getValuesTree();
        int result19 = (result18 * 59) + ($valuesTree == null ? 43 : $valuesTree.hashCode());
        Object $interfaceUrl = getInterfaceUrl();
        int result20 = (result19 * 59) + ($interfaceUrl == null ? 43 : $interfaceUrl.hashCode());
        Object $fieldFormat = getFieldFormat();
        int result21 = (result20 * 59) + ($fieldFormat == null ? 43 : $fieldFormat.hashCode());
        Object $defaultValue = getDefaultValue();
        int result22 = (result21 * 59) + ($defaultValue == null ? 43 : $defaultValue.hashCode());
        Object $roleIds = getRoleIds();
        int result23 = (result22 * 59) + ($roleIds == null ? 43 : $roleIds.hashCode());
        Object $permission = getPermission();
        int result24 = (result23 * 59) + ($permission == null ? 43 : $permission.hashCode());
        Object $tag = getTag();
        return (result24 * 59) + ($tag == null ? 43 : $tag.hashCode());
    }

    public String toString() {
        return "IssuesTypeCustomFieldDTO(id=" + getId() + ", issuesTypeId=" + getIssuesTypeId() + ", customFieldId=" + getCustomFieldId() + ", name=" + getName() + ", code=" + getCode() + ", value=" + getValue() + ", values=" + getValues() + ", valuesTree=" + getValuesTree() + ", optionSource=" + getOptionSource() + ", interfaceUrl=" + getInterfaceUrl() + ", likeSearch=" + getLikeSearch() + ", isSystem=" + getIsSystem() + ", fieldFormat=" + getFieldFormat() + ", position=" + getPosition() + ", defaultValue=" + getDefaultValue() + ", created=" + getCreated() + ", required=" + getRequired() + ", sourceType=" + getSourceType() + ", viewList=" + getViewList() + ", roleIds=" + getRoleIds() + ", permission=" + getPermission() + ", tag=" + getTag() + ", searchable=" + getSearchable() + ", updated=" + getUpdated() + ", issuesId=" + getIssuesId() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getIssuesTypeId() {
        return this.issuesTypeId;
    }

    public Long getCustomFieldId() {
        return this.customFieldId;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public List<ValueListDTO> getValues() {
        return this.values;
    }

    public List<Tree<Long>> getValuesTree() {
        return this.valuesTree;
    }

    public Integer getOptionSource() {
        return this.optionSource;
    }

    public String getInterfaceUrl() {
        return this.interfaceUrl;
    }

    public Boolean getLikeSearch() {
        return this.likeSearch;
    }

    public Boolean getIsSystem() {
        return this.isSystem;
    }

    public String getFieldFormat() {
        return this.fieldFormat;
    }

    public Integer getPosition() {
        return this.position;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }

    public Boolean getCreated() {
        return this.created;
    }

    public Boolean getRequired() {
        return this.required;
    }

    public Integer getSourceType() {
        return this.sourceType;
    }

    public Boolean getViewList() {
        return this.viewList;
    }

    public String getRoleIds() {
        return this.roleIds;
    }

    public String getPermission() {
        return this.permission;
    }

    public String getTag() {
        return this.tag;
    }

    public Boolean getSearchable() {
        return this.searchable;
    }

    public Boolean getUpdated() {
        return this.updated;
    }

    public Long getIssuesId() {
        return this.issuesId;
    }
}
