package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
/* loaded from: DevopsIssuesDTO.class */
public class DevopsIssuesDTO {
    @ApiModelProperty("工作项id")
    private Long id;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("工作项编号")
    private String code;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项类型id")
    private Long issuesClassicId;
    @ApiModelProperty("工作项类型名称")
    private String issuesClassicName;
    @ApiModelProperty("负责人")
    private String assignedName;
    @ApiModelProperty("资源类型")
    private String resourceTypeCode;
    @ApiModelProperty("迭代id")
    private Long sprintId;
    @ApiModelProperty("迭代名称")
    private String sprint;
    @ApiModelProperty("版本id")
    private Long versionId;
    @ApiModelProperty("版本名称")
    private String version;
    @ApiModelProperty("状态id")
    private Long statusId;
    @ApiModelProperty("状态名称")
    private String status;
    @ApiModelProperty("优先级id")
    private Long priorityId;
    @ApiModelProperty("优先级名称")
    private String priority;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectId(final Long projectId) {
        this.projectId = projectId;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setIssuesClassicId(final Long issuesClassicId) {
        this.issuesClassicId = issuesClassicId;
    }

    public void setIssuesClassicName(final String issuesClassicName) {
        this.issuesClassicName = issuesClassicName;
    }

    public void setAssignedName(final String assignedName) {
        this.assignedName = assignedName;
    }

    public void setResourceTypeCode(final String resourceTypeCode) {
        this.resourceTypeCode = resourceTypeCode;
    }

    public void setSprintId(final Long sprintId) {
        this.sprintId = sprintId;
    }

    public void setSprint(final String sprint) {
        this.sprint = sprint;
    }

    public void setVersionId(final Long versionId) {
        this.versionId = versionId;
    }

    public void setVersion(final String version) {
        this.version = version;
    }

    public void setStatusId(final Long statusId) {
        this.statusId = statusId;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public void setPriorityId(final Long priorityId) {
        this.priorityId = priorityId;
    }

    public void setPriority(final String priority) {
        this.priority = priority;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof DevopsIssuesDTO) {
            DevopsIssuesDTO other = (DevopsIssuesDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$projectId = getProjectId();
                Object other$projectId = other.getProjectId();
                if (this$projectId == null) {
                    if (other$projectId != null) {
                        return false;
                    }
                } else if (!this$projectId.equals(other$projectId)) {
                    return false;
                }
                Object this$issuesClassicId = getIssuesClassicId();
                Object other$issuesClassicId = other.getIssuesClassicId();
                if (this$issuesClassicId == null) {
                    if (other$issuesClassicId != null) {
                        return false;
                    }
                } else if (!this$issuesClassicId.equals(other$issuesClassicId)) {
                    return false;
                }
                Object this$sprintId = getSprintId();
                Object other$sprintId = other.getSprintId();
                if (this$sprintId == null) {
                    if (other$sprintId != null) {
                        return false;
                    }
                } else if (!this$sprintId.equals(other$sprintId)) {
                    return false;
                }
                Object this$versionId = getVersionId();
                Object other$versionId = other.getVersionId();
                if (this$versionId == null) {
                    if (other$versionId != null) {
                        return false;
                    }
                } else if (!this$versionId.equals(other$versionId)) {
                    return false;
                }
                Object this$statusId = getStatusId();
                Object other$statusId = other.getStatusId();
                if (this$statusId == null) {
                    if (other$statusId != null) {
                        return false;
                    }
                } else if (!this$statusId.equals(other$statusId)) {
                    return false;
                }
                Object this$priorityId = getPriorityId();
                Object other$priorityId = other.getPriorityId();
                if (this$priorityId == null) {
                    if (other$priorityId != null) {
                        return false;
                    }
                } else if (!this$priorityId.equals(other$priorityId)) {
                    return false;
                }
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$issuesClassicName = getIssuesClassicName();
                Object other$issuesClassicName = other.getIssuesClassicName();
                if (this$issuesClassicName == null) {
                    if (other$issuesClassicName != null) {
                        return false;
                    }
                } else if (!this$issuesClassicName.equals(other$issuesClassicName)) {
                    return false;
                }
                Object this$assignedName = getAssignedName();
                Object other$assignedName = other.getAssignedName();
                if (this$assignedName == null) {
                    if (other$assignedName != null) {
                        return false;
                    }
                } else if (!this$assignedName.equals(other$assignedName)) {
                    return false;
                }
                Object this$resourceTypeCode = getResourceTypeCode();
                Object other$resourceTypeCode = other.getResourceTypeCode();
                if (this$resourceTypeCode == null) {
                    if (other$resourceTypeCode != null) {
                        return false;
                    }
                } else if (!this$resourceTypeCode.equals(other$resourceTypeCode)) {
                    return false;
                }
                Object this$sprint = getSprint();
                Object other$sprint = other.getSprint();
                if (this$sprint == null) {
                    if (other$sprint != null) {
                        return false;
                    }
                } else if (!this$sprint.equals(other$sprint)) {
                    return false;
                }
                Object this$version = getVersion();
                Object other$version = other.getVersion();
                if (this$version == null) {
                    if (other$version != null) {
                        return false;
                    }
                } else if (!this$version.equals(other$version)) {
                    return false;
                }
                Object this$status = getStatus();
                Object other$status = other.getStatus();
                if (this$status == null) {
                    if (other$status != null) {
                        return false;
                    }
                } else if (!this$status.equals(other$status)) {
                    return false;
                }
                Object this$priority = getPriority();
                Object other$priority = other.getPriority();
                return this$priority == null ? other$priority == null : this$priority.equals(other$priority);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof DevopsIssuesDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $projectId = getProjectId();
        int result2 = (result * 59) + ($projectId == null ? 43 : $projectId.hashCode());
        Object $issuesClassicId = getIssuesClassicId();
        int result3 = (result2 * 59) + ($issuesClassicId == null ? 43 : $issuesClassicId.hashCode());
        Object $sprintId = getSprintId();
        int result4 = (result3 * 59) + ($sprintId == null ? 43 : $sprintId.hashCode());
        Object $versionId = getVersionId();
        int result5 = (result4 * 59) + ($versionId == null ? 43 : $versionId.hashCode());
        Object $statusId = getStatusId();
        int result6 = (result5 * 59) + ($statusId == null ? 43 : $statusId.hashCode());
        Object $priorityId = getPriorityId();
        int result7 = (result6 * 59) + ($priorityId == null ? 43 : $priorityId.hashCode());
        Object $code = getCode();
        int result8 = (result7 * 59) + ($code == null ? 43 : $code.hashCode());
        Object $name = getName();
        int result9 = (result8 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $issuesClassicName = getIssuesClassicName();
        int result10 = (result9 * 59) + ($issuesClassicName == null ? 43 : $issuesClassicName.hashCode());
        Object $assignedName = getAssignedName();
        int result11 = (result10 * 59) + ($assignedName == null ? 43 : $assignedName.hashCode());
        Object $resourceTypeCode = getResourceTypeCode();
        int result12 = (result11 * 59) + ($resourceTypeCode == null ? 43 : $resourceTypeCode.hashCode());
        Object $sprint = getSprint();
        int result13 = (result12 * 59) + ($sprint == null ? 43 : $sprint.hashCode());
        Object $version = getVersion();
        int result14 = (result13 * 59) + ($version == null ? 43 : $version.hashCode());
        Object $status = getStatus();
        int result15 = (result14 * 59) + ($status == null ? 43 : $status.hashCode());
        Object $priority = getPriority();
        return (result15 * 59) + ($priority == null ? 43 : $priority.hashCode());
    }

    public String toString() {
        return "DevopsIssuesDTO(id=" + getId() + ", projectId=" + getProjectId() + ", code=" + getCode() + ", name=" + getName() + ", issuesClassicId=" + getIssuesClassicId() + ", issuesClassicName=" + getIssuesClassicName() + ", assignedName=" + getAssignedName() + ", resourceTypeCode=" + getResourceTypeCode() + ", sprintId=" + getSprintId() + ", sprint=" + getSprint() + ", versionId=" + getVersionId() + ", version=" + getVersion() + ", statusId=" + getStatusId() + ", status=" + getStatus() + ", priorityId=" + getPriorityId() + ", priority=" + getPriority() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getProjectId() {
        return this.projectId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Long getIssuesClassicId() {
        return this.issuesClassicId;
    }

    public String getIssuesClassicName() {
        return this.issuesClassicName;
    }

    public String getAssignedName() {
        return this.assignedName;
    }

    public String getResourceTypeCode() {
        return this.resourceTypeCode;
    }

    public Long getSprintId() {
        return this.sprintId;
    }

    public String getSprint() {
        return this.sprint;
    }

    public Long getVersionId() {
        return this.versionId;
    }

    public String getVersion() {
        return this.version;
    }

    public Long getStatusId() {
        return this.statusId;
    }

    public String getStatus() {
        return this.status;
    }

    public Long getPriorityId() {
        return this.priorityId;
    }

    public String getPriority() {
        return this.priority;
    }
}
