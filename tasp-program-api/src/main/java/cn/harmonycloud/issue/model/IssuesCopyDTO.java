package cn.harmonycloud.issue.model;

import cn.harmonycloud.issue.enums.IssuesRelationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;

@ApiModel
/* loaded from: IssuesCopyDTO.class */
public class IssuesCopyDTO {
    @NotNull
    @ApiModelProperty("事项id")
    private Long id;
    @ApiModelProperty("关联事项类型，默认为复制")
    private Long relationType = IssuesRelationTypeEnum.DUPLICATE.getId();

    public void setId(final Long id) {
        this.id = id;
    }

    public void setRelationType(final Long relationType) {
        this.relationType = relationType;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesCopyDTO) {
            IssuesCopyDTO other = (IssuesCopyDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$relationType = getRelationType();
                Object other$relationType = other.getRelationType();
                return this$relationType == null ? other$relationType == null : this$relationType.equals(other$relationType);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesCopyDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $relationType = getRelationType();
        return (result * 59) + ($relationType == null ? 43 : $relationType.hashCode());
    }

    public String toString() {
        return "IssuesCopyDTO(id=" + getId() + ", relationType=" + getRelationType() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getRelationType() {
        return this.relationType;
    }
}
