package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
/* loaded from: StatisticsTypeAndCountDTO.class */
public class StatisticsTypeAndCountDTO {
    @ApiModelProperty("状态阶段")
    private Long type;
    @ApiModelProperty("事项数量")
    private Integer issuesCount;

    public void setType(final Long type) {
        this.type = type;
    }

    public void setIssuesCount(final Integer issuesCount) {
        this.issuesCount = issuesCount;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof StatisticsTypeAndCountDTO) {
            StatisticsTypeAndCountDTO other = (StatisticsTypeAndCountDTO) o;
            if (other.canEqual(this)) {
                Object this$type = getType();
                Object other$type = other.getType();
                if (this$type == null) {
                    if (other$type != null) {
                        return false;
                    }
                } else if (!this$type.equals(other$type)) {
                    return false;
                }
                Object this$issuesCount = getIssuesCount();
                Object other$issuesCount = other.getIssuesCount();
                return this$issuesCount == null ? other$issuesCount == null : this$issuesCount.equals(other$issuesCount);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof StatisticsTypeAndCountDTO;
    }

    public int hashCode() {
        Object $type = getType();
        int result = (1 * 59) + ($type == null ? 43 : $type.hashCode());
        Object $issuesCount = getIssuesCount();
        return (result * 59) + ($issuesCount == null ? 43 : $issuesCount.hashCode());
    }

    public String toString() {
        return "StatisticsTypeAndCountDTO(type=" + getType() + ", issuesCount=" + getIssuesCount() + ")";
    }

    public Long getType() {
        return this.type;
    }

    public Integer getIssuesCount() {
        return this.issuesCount;
    }
}
