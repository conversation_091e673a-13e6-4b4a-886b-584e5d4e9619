package cn.harmonycloud.issue.model.vo.v2;

import cn.harmonycloud.issue.model.IssuesFieldDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

/* loaded from: ProjectQueryVO.class */
public class ProjectQueryVO {
    private Long id;
    private String projectCode;
    private String name;
    private String description;
    private String belongOrgId;
    private BaseVO processDictionary;
    private BaseVO typeDictionary;
    private BaseVO sourceDictionary;
    private String sourceId;
    private BaseVO statusDictionary;
    private List<UserVO> principalList;
    private List<UserVO> assistantPrincipalList;
    private String createTime;
    private Long templateId;
    private String startTime;
    private String endTime;
    private String projectCycle;
    private String principalName;
    private String assistantPrincipalName;
    private BigDecimal schedule;
    private Long processDictionaryId;
    private Long typeDictionaryId;
    private Long sourceDictionaryId;
    private Long statusDictionaryId;
    private String queryParam;
    private Long principalId;
    private Long edepId;
    private String appProjectId;
    private String createBy;
    private String statusDictionaryName;
    private MilestoneVO milestoneVO;
    private Long businessProjectId;
    private String businessProjectName;
    @ApiModelProperty(name = "拓展字段")
    private List<IssuesFieldDTO> customFieldsList;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectCode(final String projectCode) {
        this.projectCode = projectCode;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public void setBelongOrgId(final String belongOrgId) {
        this.belongOrgId = belongOrgId;
    }

    public void setProcessDictionary(final BaseVO processDictionary) {
        this.processDictionary = processDictionary;
    }

    public void setTypeDictionary(final BaseVO typeDictionary) {
        this.typeDictionary = typeDictionary;
    }

    public void setSourceDictionary(final BaseVO sourceDictionary) {
        this.sourceDictionary = sourceDictionary;
    }

    public void setSourceId(final String sourceId) {
        this.sourceId = sourceId;
    }

    public void setStatusDictionary(final BaseVO statusDictionary) {
        this.statusDictionary = statusDictionary;
    }

    public void setPrincipalList(final List<UserVO> principalList) {
        this.principalList = principalList;
    }

    public void setAssistantPrincipalList(final List<UserVO> assistantPrincipalList) {
        this.assistantPrincipalList = assistantPrincipalList;
    }

    public void setCreateTime(final String createTime) {
        this.createTime = createTime;
    }

    public void setTemplateId(final Long templateId) {
        this.templateId = templateId;
    }

    public void setStartTime(final String startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(final String endTime) {
        this.endTime = endTime;
    }

    public void setProjectCycle(final String projectCycle) {
        this.projectCycle = projectCycle;
    }

    public void setPrincipalName(final String principalName) {
        this.principalName = principalName;
    }

    public void setAssistantPrincipalName(final String assistantPrincipalName) {
        this.assistantPrincipalName = assistantPrincipalName;
    }

    public void setSchedule(final BigDecimal schedule) {
        this.schedule = schedule;
    }

    public void setProcessDictionaryId(final Long processDictionaryId) {
        this.processDictionaryId = processDictionaryId;
    }

    public void setTypeDictionaryId(final Long typeDictionaryId) {
        this.typeDictionaryId = typeDictionaryId;
    }

    public void setSourceDictionaryId(final Long sourceDictionaryId) {
        this.sourceDictionaryId = sourceDictionaryId;
    }

    public void setStatusDictionaryId(final Long statusDictionaryId) {
        this.statusDictionaryId = statusDictionaryId;
    }

    public void setQueryParam(final String queryParam) {
        this.queryParam = queryParam;
    }

    public void setPrincipalId(final Long principalId) {
        this.principalId = principalId;
    }

    public void setEdepId(final Long edepId) {
        this.edepId = edepId;
    }

    public void setAppProjectId(final String appProjectId) {
        this.appProjectId = appProjectId;
    }

    public void setCreateBy(final String createBy) {
        this.createBy = createBy;
    }

    public void setStatusDictionaryName(final String statusDictionaryName) {
        this.statusDictionaryName = statusDictionaryName;
    }

    public void setMilestoneVO(final MilestoneVO milestoneVO) {
        this.milestoneVO = milestoneVO;
    }

    public void setBusinessProjectId(final Long businessProjectId) {
        this.businessProjectId = businessProjectId;
    }

    public void setBusinessProjectName(final String businessProjectName) {
        this.businessProjectName = businessProjectName;
    }

    public void setCustomFieldsList(final List<IssuesFieldDTO> customFieldsList) {
        this.customFieldsList = customFieldsList;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectQueryVO) {
            ProjectQueryVO other = (ProjectQueryVO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$templateId = getTemplateId();
                Object other$templateId = other.getTemplateId();
                if (this$templateId == null) {
                    if (other$templateId != null) {
                        return false;
                    }
                } else if (!this$templateId.equals(other$templateId)) {
                    return false;
                }
                Object this$processDictionaryId = getProcessDictionaryId();
                Object other$processDictionaryId = other.getProcessDictionaryId();
                if (this$processDictionaryId == null) {
                    if (other$processDictionaryId != null) {
                        return false;
                    }
                } else if (!this$processDictionaryId.equals(other$processDictionaryId)) {
                    return false;
                }
                Object this$typeDictionaryId = getTypeDictionaryId();
                Object other$typeDictionaryId = other.getTypeDictionaryId();
                if (this$typeDictionaryId == null) {
                    if (other$typeDictionaryId != null) {
                        return false;
                    }
                } else if (!this$typeDictionaryId.equals(other$typeDictionaryId)) {
                    return false;
                }
                Object this$sourceDictionaryId = getSourceDictionaryId();
                Object other$sourceDictionaryId = other.getSourceDictionaryId();
                if (this$sourceDictionaryId == null) {
                    if (other$sourceDictionaryId != null) {
                        return false;
                    }
                } else if (!this$sourceDictionaryId.equals(other$sourceDictionaryId)) {
                    return false;
                }
                Object this$statusDictionaryId = getStatusDictionaryId();
                Object other$statusDictionaryId = other.getStatusDictionaryId();
                if (this$statusDictionaryId == null) {
                    if (other$statusDictionaryId != null) {
                        return false;
                    }
                } else if (!this$statusDictionaryId.equals(other$statusDictionaryId)) {
                    return false;
                }
                Object this$principalId = getPrincipalId();
                Object other$principalId = other.getPrincipalId();
                if (this$principalId == null) {
                    if (other$principalId != null) {
                        return false;
                    }
                } else if (!this$principalId.equals(other$principalId)) {
                    return false;
                }
                Object this$edepId = getEdepId();
                Object other$edepId = other.getEdepId();
                if (this$edepId == null) {
                    if (other$edepId != null) {
                        return false;
                    }
                } else if (!this$edepId.equals(other$edepId)) {
                    return false;
                }
                Object this$businessProjectId = getBusinessProjectId();
                Object other$businessProjectId = other.getBusinessProjectId();
                if (this$businessProjectId == null) {
                    if (other$businessProjectId != null) {
                        return false;
                    }
                } else if (!this$businessProjectId.equals(other$businessProjectId)) {
                    return false;
                }
                Object this$projectCode = getProjectCode();
                Object other$projectCode = other.getProjectCode();
                if (this$projectCode == null) {
                    if (other$projectCode != null) {
                        return false;
                    }
                } else if (!this$projectCode.equals(other$projectCode)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$description = getDescription();
                Object other$description = other.getDescription();
                if (this$description == null) {
                    if (other$description != null) {
                        return false;
                    }
                } else if (!this$description.equals(other$description)) {
                    return false;
                }
                Object this$belongOrgId = getBelongOrgId();
                Object other$belongOrgId = other.getBelongOrgId();
                if (this$belongOrgId == null) {
                    if (other$belongOrgId != null) {
                        return false;
                    }
                } else if (!this$belongOrgId.equals(other$belongOrgId)) {
                    return false;
                }
                Object this$processDictionary = getProcessDictionary();
                Object other$processDictionary = other.getProcessDictionary();
                if (this$processDictionary == null) {
                    if (other$processDictionary != null) {
                        return false;
                    }
                } else if (!this$processDictionary.equals(other$processDictionary)) {
                    return false;
                }
                Object this$typeDictionary = getTypeDictionary();
                Object other$typeDictionary = other.getTypeDictionary();
                if (this$typeDictionary == null) {
                    if (other$typeDictionary != null) {
                        return false;
                    }
                } else if (!this$typeDictionary.equals(other$typeDictionary)) {
                    return false;
                }
                Object this$sourceDictionary = getSourceDictionary();
                Object other$sourceDictionary = other.getSourceDictionary();
                if (this$sourceDictionary == null) {
                    if (other$sourceDictionary != null) {
                        return false;
                    }
                } else if (!this$sourceDictionary.equals(other$sourceDictionary)) {
                    return false;
                }
                Object this$sourceId = getSourceId();
                Object other$sourceId = other.getSourceId();
                if (this$sourceId == null) {
                    if (other$sourceId != null) {
                        return false;
                    }
                } else if (!this$sourceId.equals(other$sourceId)) {
                    return false;
                }
                Object this$statusDictionary = getStatusDictionary();
                Object other$statusDictionary = other.getStatusDictionary();
                if (this$statusDictionary == null) {
                    if (other$statusDictionary != null) {
                        return false;
                    }
                } else if (!this$statusDictionary.equals(other$statusDictionary)) {
                    return false;
                }
                Object this$principalList = getPrincipalList();
                Object other$principalList = other.getPrincipalList();
                if (this$principalList == null) {
                    if (other$principalList != null) {
                        return false;
                    }
                } else if (!this$principalList.equals(other$principalList)) {
                    return false;
                }
                Object this$assistantPrincipalList = getAssistantPrincipalList();
                Object other$assistantPrincipalList = other.getAssistantPrincipalList();
                if (this$assistantPrincipalList == null) {
                    if (other$assistantPrincipalList != null) {
                        return false;
                    }
                } else if (!this$assistantPrincipalList.equals(other$assistantPrincipalList)) {
                    return false;
                }
                Object this$createTime = getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }
                Object this$startTime = getStartTime();
                Object other$startTime = other.getStartTime();
                if (this$startTime == null) {
                    if (other$startTime != null) {
                        return false;
                    }
                } else if (!this$startTime.equals(other$startTime)) {
                    return false;
                }
                Object this$endTime = getEndTime();
                Object other$endTime = other.getEndTime();
                if (this$endTime == null) {
                    if (other$endTime != null) {
                        return false;
                    }
                } else if (!this$endTime.equals(other$endTime)) {
                    return false;
                }
                Object this$projectCycle = getProjectCycle();
                Object other$projectCycle = other.getProjectCycle();
                if (this$projectCycle == null) {
                    if (other$projectCycle != null) {
                        return false;
                    }
                } else if (!this$projectCycle.equals(other$projectCycle)) {
                    return false;
                }
                Object this$principalName = getPrincipalName();
                Object other$principalName = other.getPrincipalName();
                if (this$principalName == null) {
                    if (other$principalName != null) {
                        return false;
                    }
                } else if (!this$principalName.equals(other$principalName)) {
                    return false;
                }
                Object this$assistantPrincipalName = getAssistantPrincipalName();
                Object other$assistantPrincipalName = other.getAssistantPrincipalName();
                if (this$assistantPrincipalName == null) {
                    if (other$assistantPrincipalName != null) {
                        return false;
                    }
                } else if (!this$assistantPrincipalName.equals(other$assistantPrincipalName)) {
                    return false;
                }
                Object this$schedule = getSchedule();
                Object other$schedule = other.getSchedule();
                if (this$schedule == null) {
                    if (other$schedule != null) {
                        return false;
                    }
                } else if (!this$schedule.equals(other$schedule)) {
                    return false;
                }
                Object this$queryParam = getQueryParam();
                Object other$queryParam = other.getQueryParam();
                if (this$queryParam == null) {
                    if (other$queryParam != null) {
                        return false;
                    }
                } else if (!this$queryParam.equals(other$queryParam)) {
                    return false;
                }
                Object this$appProjectId = getAppProjectId();
                Object other$appProjectId = other.getAppProjectId();
                if (this$appProjectId == null) {
                    if (other$appProjectId != null) {
                        return false;
                    }
                } else if (!this$appProjectId.equals(other$appProjectId)) {
                    return false;
                }
                Object this$createBy = getCreateBy();
                Object other$createBy = other.getCreateBy();
                if (this$createBy == null) {
                    if (other$createBy != null) {
                        return false;
                    }
                } else if (!this$createBy.equals(other$createBy)) {
                    return false;
                }
                Object this$statusDictionaryName = getStatusDictionaryName();
                Object other$statusDictionaryName = other.getStatusDictionaryName();
                if (this$statusDictionaryName == null) {
                    if (other$statusDictionaryName != null) {
                        return false;
                    }
                } else if (!this$statusDictionaryName.equals(other$statusDictionaryName)) {
                    return false;
                }
                Object this$milestoneVO = getMilestoneVO();
                Object other$milestoneVO = other.getMilestoneVO();
                if (this$milestoneVO == null) {
                    if (other$milestoneVO != null) {
                        return false;
                    }
                } else if (!this$milestoneVO.equals(other$milestoneVO)) {
                    return false;
                }
                Object this$businessProjectName = getBusinessProjectName();
                Object other$businessProjectName = other.getBusinessProjectName();
                if (this$businessProjectName == null) {
                    if (other$businessProjectName != null) {
                        return false;
                    }
                } else if (!this$businessProjectName.equals(other$businessProjectName)) {
                    return false;
                }
                Object this$customFieldsList = getCustomFieldsList();
                Object other$customFieldsList = other.getCustomFieldsList();
                return this$customFieldsList == null ? other$customFieldsList == null : this$customFieldsList.equals(other$customFieldsList);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectQueryVO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $templateId = getTemplateId();
        int result2 = (result * 59) + ($templateId == null ? 43 : $templateId.hashCode());
        Object $processDictionaryId = getProcessDictionaryId();
        int result3 = (result2 * 59) + ($processDictionaryId == null ? 43 : $processDictionaryId.hashCode());
        Object $typeDictionaryId = getTypeDictionaryId();
        int result4 = (result3 * 59) + ($typeDictionaryId == null ? 43 : $typeDictionaryId.hashCode());
        Object $sourceDictionaryId = getSourceDictionaryId();
        int result5 = (result4 * 59) + ($sourceDictionaryId == null ? 43 : $sourceDictionaryId.hashCode());
        Object $statusDictionaryId = getStatusDictionaryId();
        int result6 = (result5 * 59) + ($statusDictionaryId == null ? 43 : $statusDictionaryId.hashCode());
        Object $principalId = getPrincipalId();
        int result7 = (result6 * 59) + ($principalId == null ? 43 : $principalId.hashCode());
        Object $edepId = getEdepId();
        int result8 = (result7 * 59) + ($edepId == null ? 43 : $edepId.hashCode());
        Object $businessProjectId = getBusinessProjectId();
        int result9 = (result8 * 59) + ($businessProjectId == null ? 43 : $businessProjectId.hashCode());
        Object $projectCode = getProjectCode();
        int result10 = (result9 * 59) + ($projectCode == null ? 43 : $projectCode.hashCode());
        Object $name = getName();
        int result11 = (result10 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $description = getDescription();
        int result12 = (result11 * 59) + ($description == null ? 43 : $description.hashCode());
        Object $belongOrgId = getBelongOrgId();
        int result13 = (result12 * 59) + ($belongOrgId == null ? 43 : $belongOrgId.hashCode());
        Object $processDictionary = getProcessDictionary();
        int result14 = (result13 * 59) + ($processDictionary == null ? 43 : $processDictionary.hashCode());
        Object $typeDictionary = getTypeDictionary();
        int result15 = (result14 * 59) + ($typeDictionary == null ? 43 : $typeDictionary.hashCode());
        Object $sourceDictionary = getSourceDictionary();
        int result16 = (result15 * 59) + ($sourceDictionary == null ? 43 : $sourceDictionary.hashCode());
        Object $sourceId = getSourceId();
        int result17 = (result16 * 59) + ($sourceId == null ? 43 : $sourceId.hashCode());
        Object $statusDictionary = getStatusDictionary();
        int result18 = (result17 * 59) + ($statusDictionary == null ? 43 : $statusDictionary.hashCode());
        Object $principalList = getPrincipalList();
        int result19 = (result18 * 59) + ($principalList == null ? 43 : $principalList.hashCode());
        Object $assistantPrincipalList = getAssistantPrincipalList();
        int result20 = (result19 * 59) + ($assistantPrincipalList == null ? 43 : $assistantPrincipalList.hashCode());
        Object $createTime = getCreateTime();
        int result21 = (result20 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $startTime = getStartTime();
        int result22 = (result21 * 59) + ($startTime == null ? 43 : $startTime.hashCode());
        Object $endTime = getEndTime();
        int result23 = (result22 * 59) + ($endTime == null ? 43 : $endTime.hashCode());
        Object $projectCycle = getProjectCycle();
        int result24 = (result23 * 59) + ($projectCycle == null ? 43 : $projectCycle.hashCode());
        Object $principalName = getPrincipalName();
        int result25 = (result24 * 59) + ($principalName == null ? 43 : $principalName.hashCode());
        Object $assistantPrincipalName = getAssistantPrincipalName();
        int result26 = (result25 * 59) + ($assistantPrincipalName == null ? 43 : $assistantPrincipalName.hashCode());
        Object $schedule = getSchedule();
        int result27 = (result26 * 59) + ($schedule == null ? 43 : $schedule.hashCode());
        Object $queryParam = getQueryParam();
        int result28 = (result27 * 59) + ($queryParam == null ? 43 : $queryParam.hashCode());
        Object $appProjectId = getAppProjectId();
        int result29 = (result28 * 59) + ($appProjectId == null ? 43 : $appProjectId.hashCode());
        Object $createBy = getCreateBy();
        int result30 = (result29 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $statusDictionaryName = getStatusDictionaryName();
        int result31 = (result30 * 59) + ($statusDictionaryName == null ? 43 : $statusDictionaryName.hashCode());
        Object $milestoneVO = getMilestoneVO();
        int result32 = (result31 * 59) + ($milestoneVO == null ? 43 : $milestoneVO.hashCode());
        Object $businessProjectName = getBusinessProjectName();
        int result33 = (result32 * 59) + ($businessProjectName == null ? 43 : $businessProjectName.hashCode());
        Object $customFieldsList = getCustomFieldsList();
        return (result33 * 59) + ($customFieldsList == null ? 43 : $customFieldsList.hashCode());
    }

    public String toString() {
        return "ProjectQueryVO(id=" + getId() + ", projectCode=" + getProjectCode() + ", name=" + getName() + ", description=" + getDescription() + ", belongOrgId=" + getBelongOrgId() + ", processDictionary=" + getProcessDictionary() + ", typeDictionary=" + getTypeDictionary() + ", sourceDictionary=" + getSourceDictionary() + ", sourceId=" + getSourceId() + ", statusDictionary=" + getStatusDictionary() + ", principalList=" + getPrincipalList() + ", assistantPrincipalList=" + getAssistantPrincipalList() + ", createTime=" + getCreateTime() + ", templateId=" + getTemplateId() + ", startTime=" + getStartTime() + ", endTime=" + getEndTime() + ", projectCycle=" + getProjectCycle() + ", principalName=" + getPrincipalName() + ", assistantPrincipalName=" + getAssistantPrincipalName() + ", schedule=" + getSchedule() + ", processDictionaryId=" + getProcessDictionaryId() + ", typeDictionaryId=" + getTypeDictionaryId() + ", sourceDictionaryId=" + getSourceDictionaryId() + ", statusDictionaryId=" + getStatusDictionaryId() + ", queryParam=" + getQueryParam() + ", principalId=" + getPrincipalId() + ", edepId=" + getEdepId() + ", appProjectId=" + getAppProjectId() + ", createBy=" + getCreateBy() + ", statusDictionaryName=" + getStatusDictionaryName() + ", milestoneVO=" + getMilestoneVO() + ", businessProjectId=" + getBusinessProjectId() + ", businessProjectName=" + getBusinessProjectName() + ", customFieldsList=" + getCustomFieldsList() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public String getProjectCode() {
        return this.projectCode;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public String getBelongOrgId() {
        return this.belongOrgId;
    }

    public BaseVO getProcessDictionary() {
        return this.processDictionary;
    }

    public BaseVO getTypeDictionary() {
        return this.typeDictionary;
    }

    public BaseVO getSourceDictionary() {
        return this.sourceDictionary;
    }

    public String getSourceId() {
        return this.sourceId;
    }

    public BaseVO getStatusDictionary() {
        return this.statusDictionary;
    }

    public List<UserVO> getPrincipalList() {
        return this.principalList;
    }

    public List<UserVO> getAssistantPrincipalList() {
        return this.assistantPrincipalList;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public Long getTemplateId() {
        return this.templateId;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public String getProjectCycle() {
        return this.projectCycle;
    }

    public String getPrincipalName() {
        return this.principalName;
    }

    public String getAssistantPrincipalName() {
        return this.assistantPrincipalName;
    }

    public BigDecimal getSchedule() {
        return this.schedule;
    }

    public Long getProcessDictionaryId() {
        return this.processDictionaryId;
    }

    public Long getTypeDictionaryId() {
        return this.typeDictionaryId;
    }

    public Long getSourceDictionaryId() {
        return this.sourceDictionaryId;
    }

    public Long getStatusDictionaryId() {
        return this.statusDictionaryId;
    }

    public String getQueryParam() {
        return this.queryParam;
    }

    public Long getPrincipalId() {
        return this.principalId;
    }

    public Long getEdepId() {
        return this.edepId;
    }

    public String getAppProjectId() {
        return this.appProjectId;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public String getStatusDictionaryName() {
        return this.statusDictionaryName;
    }

    public MilestoneVO getMilestoneVO() {
        return this.milestoneVO;
    }

    public Long getBusinessProjectId() {
        return this.businessProjectId;
    }

    public String getBusinessProjectName() {
        return this.businessProjectName;
    }

    public List<IssuesFieldDTO> getCustomFieldsList() {
        return this.customFieldsList;
    }
}
