package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("交接")
/* loaded from: ProjectMemberHandoverDTO.class */
public class ProjectMemberHandoverDTO {
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("项目成员id")
    private Long memberUserId;
    @ApiModelProperty("交接用户id")
    private Long transmitUserId;

    public void setProjectId(final Long projectId) {
        this.projectId = projectId;
    }

    public void setMemberUserId(final Long memberUserId) {
        this.memberUserId = memberUserId;
    }

    public void setTransmitUserId(final Long transmitUserId) {
        this.transmitUserId = transmitUserId;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectMemberHandoverDTO) {
            ProjectMemberHandoverDTO other = (ProjectMemberHandoverDTO) o;
            if (other.canEqual(this)) {
                Object this$projectId = getProjectId();
                Object other$projectId = other.getProjectId();
                if (this$projectId == null) {
                    if (other$projectId != null) {
                        return false;
                    }
                } else if (!this$projectId.equals(other$projectId)) {
                    return false;
                }
                Object this$memberUserId = getMemberUserId();
                Object other$memberUserId = other.getMemberUserId();
                if (this$memberUserId == null) {
                    if (other$memberUserId != null) {
                        return false;
                    }
                } else if (!this$memberUserId.equals(other$memberUserId)) {
                    return false;
                }
                Object this$transmitUserId = getTransmitUserId();
                Object other$transmitUserId = other.getTransmitUserId();
                return this$transmitUserId == null ? other$transmitUserId == null : this$transmitUserId.equals(other$transmitUserId);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectMemberHandoverDTO;
    }

    public int hashCode() {
        Object $projectId = getProjectId();
        int result = (1 * 59) + ($projectId == null ? 43 : $projectId.hashCode());
        Object $memberUserId = getMemberUserId();
        int result2 = (result * 59) + ($memberUserId == null ? 43 : $memberUserId.hashCode());
        Object $transmitUserId = getTransmitUserId();
        return (result2 * 59) + ($transmitUserId == null ? 43 : $transmitUserId.hashCode());
    }

    public String toString() {
        return "ProjectMemberHandoverDTO(projectId=" + getProjectId() + ", memberUserId=" + getMemberUserId() + ", transmitUserId=" + getTransmitUserId() + ")";
    }

    public Long getProjectId() {
        return this.projectId;
    }

    public Long getMemberUserId() {
        return this.memberUserId;
    }

    public Long getTransmitUserId() {
        return this.transmitUserId;
    }
}
