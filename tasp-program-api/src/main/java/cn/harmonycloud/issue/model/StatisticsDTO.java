package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel("不同事项类型下根据状态阶段统计事项")
/* loaded from: StatisticsDTO.class */
public class StatisticsDTO {
    @ApiModelProperty("事项类型id")
    private Long issuesTypeId;
    @ApiModelProperty("每个阶段下包含的事项数量")
    private List<StatisticsTypeAndCountDTO> typeAndCountDTOList;

    public void setIssuesTypeId(final Long issuesTypeId) {
        this.issuesTypeId = issuesTypeId;
    }

    public void setTypeAndCountDTOList(final List<StatisticsTypeAndCountDTO> typeAndCountDTOList) {
        this.typeAndCountDTOList = typeAndCountDTOList;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof StatisticsDTO) {
            StatisticsDTO other = (StatisticsDTO) o;
            if (other.canEqual(this)) {
                Object this$issuesTypeId = getIssuesTypeId();
                Object other$issuesTypeId = other.getIssuesTypeId();
                if (this$issuesTypeId == null) {
                    if (other$issuesTypeId != null) {
                        return false;
                    }
                } else if (!this$issuesTypeId.equals(other$issuesTypeId)) {
                    return false;
                }
                Object this$typeAndCountDTOList = getTypeAndCountDTOList();
                Object other$typeAndCountDTOList = other.getTypeAndCountDTOList();
                return this$typeAndCountDTOList == null ? other$typeAndCountDTOList == null : this$typeAndCountDTOList.equals(other$typeAndCountDTOList);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof StatisticsDTO;
    }

    public int hashCode() {
        Object $issuesTypeId = getIssuesTypeId();
        int result = (1 * 59) + ($issuesTypeId == null ? 43 : $issuesTypeId.hashCode());
        Object $typeAndCountDTOList = getTypeAndCountDTOList();
        return (result * 59) + ($typeAndCountDTOList == null ? 43 : $typeAndCountDTOList.hashCode());
    }

    public String toString() {
        return "StatisticsDTO(issuesTypeId=" + getIssuesTypeId() + ", typeAndCountDTOList=" + getTypeAndCountDTOList() + ")";
    }

    public Long getIssuesTypeId() {
        return this.issuesTypeId;
    }

    public List<StatisticsTypeAndCountDTO> getTypeAndCountDTOList() {
        return this.typeAndCountDTOList;
    }
}
