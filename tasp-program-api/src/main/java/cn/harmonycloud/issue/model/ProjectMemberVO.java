package cn.harmonycloud.issue.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/* loaded from: ProjectMemberVO.class */
public class ProjectMemberVO implements Serializable {
    @ApiModelProperty(name = "用户ID")
    private String userId;
    @ApiModelProperty(name = "姓名")
    private String userName;
    @ApiModelProperty(name = "角色")
    private String roleName;
    @ApiModelProperty(name = "加入时间")
    private String createTime;
    @ApiModelProperty(name = "投入工时")
    private Double workingHour;
    @ApiModelProperty(name = "任务量")
    private Integer taskCount;
    @ApiModelProperty(name = "缺陷量")
    private Integer bugCount;
    @ApiModelProperty(name = "完成及时率")
    private Double finishedTimelinessRate;
    @ApiModelProperty(name = "风险数")
    private Integer riskCount;

    public void setUserId(final String userId) {
        this.userId = userId;
    }

    public void setUserName(final String userName) {
        this.userName = userName;
    }

    public void setRoleName(final String roleName) {
        this.roleName = roleName;
    }

    public void setCreateTime(final String createTime) {
        this.createTime = createTime;
    }

    public void setWorkingHour(final Double workingHour) {
        this.workingHour = workingHour;
    }

    public void setTaskCount(final Integer taskCount) {
        this.taskCount = taskCount;
    }

    public void setBugCount(final Integer bugCount) {
        this.bugCount = bugCount;
    }

    public void setFinishedTimelinessRate(final Double finishedTimelinessRate) {
        this.finishedTimelinessRate = finishedTimelinessRate;
    }

    public void setRiskCount(final Integer riskCount) {
        this.riskCount = riskCount;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof ProjectMemberVO) {
            ProjectMemberVO other = (ProjectMemberVO) o;
            if (other.canEqual(this)) {
                Object this$workingHour = getWorkingHour();
                Object other$workingHour = other.getWorkingHour();
                if (this$workingHour == null) {
                    if (other$workingHour != null) {
                        return false;
                    }
                } else if (!this$workingHour.equals(other$workingHour)) {
                    return false;
                }
                Object this$taskCount = getTaskCount();
                Object other$taskCount = other.getTaskCount();
                if (this$taskCount == null) {
                    if (other$taskCount != null) {
                        return false;
                    }
                } else if (!this$taskCount.equals(other$taskCount)) {
                    return false;
                }
                Object this$bugCount = getBugCount();
                Object other$bugCount = other.getBugCount();
                if (this$bugCount == null) {
                    if (other$bugCount != null) {
                        return false;
                    }
                } else if (!this$bugCount.equals(other$bugCount)) {
                    return false;
                }
                Object this$finishedTimelinessRate = getFinishedTimelinessRate();
                Object other$finishedTimelinessRate = other.getFinishedTimelinessRate();
                if (this$finishedTimelinessRate == null) {
                    if (other$finishedTimelinessRate != null) {
                        return false;
                    }
                } else if (!this$finishedTimelinessRate.equals(other$finishedTimelinessRate)) {
                    return false;
                }
                Object this$riskCount = getRiskCount();
                Object other$riskCount = other.getRiskCount();
                if (this$riskCount == null) {
                    if (other$riskCount != null) {
                        return false;
                    }
                } else if (!this$riskCount.equals(other$riskCount)) {
                    return false;
                }
                Object this$userId = getUserId();
                Object other$userId = other.getUserId();
                if (this$userId == null) {
                    if (other$userId != null) {
                        return false;
                    }
                } else if (!this$userId.equals(other$userId)) {
                    return false;
                }
                Object this$userName = getUserName();
                Object other$userName = other.getUserName();
                if (this$userName == null) {
                    if (other$userName != null) {
                        return false;
                    }
                } else if (!this$userName.equals(other$userName)) {
                    return false;
                }
                Object this$roleName = getRoleName();
                Object other$roleName = other.getRoleName();
                if (this$roleName == null) {
                    if (other$roleName != null) {
                        return false;
                    }
                } else if (!this$roleName.equals(other$roleName)) {
                    return false;
                }
                Object this$createTime = getCreateTime();
                Object other$createTime = other.getCreateTime();
                return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ProjectMemberVO;
    }

    public int hashCode() {
        Object $workingHour = getWorkingHour();
        int result = (1 * 59) + ($workingHour == null ? 43 : $workingHour.hashCode());
        Object $taskCount = getTaskCount();
        int result2 = (result * 59) + ($taskCount == null ? 43 : $taskCount.hashCode());
        Object $bugCount = getBugCount();
        int result3 = (result2 * 59) + ($bugCount == null ? 43 : $bugCount.hashCode());
        Object $finishedTimelinessRate = getFinishedTimelinessRate();
        int result4 = (result3 * 59) + ($finishedTimelinessRate == null ? 43 : $finishedTimelinessRate.hashCode());
        Object $riskCount = getRiskCount();
        int result5 = (result4 * 59) + ($riskCount == null ? 43 : $riskCount.hashCode());
        Object $userId = getUserId();
        int result6 = (result5 * 59) + ($userId == null ? 43 : $userId.hashCode());
        Object $userName = getUserName();
        int result7 = (result6 * 59) + ($userName == null ? 43 : $userName.hashCode());
        Object $roleName = getRoleName();
        int result8 = (result7 * 59) + ($roleName == null ? 43 : $roleName.hashCode());
        Object $createTime = getCreateTime();
        return (result8 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }

    public String toString() {
        return "ProjectMemberVO(userId=" + getUserId() + ", userName=" + getUserName() + ", roleName=" + getRoleName() + ", createTime=" + getCreateTime() + ", workingHour=" + getWorkingHour() + ", taskCount=" + getTaskCount() + ", bugCount=" + getBugCount() + ", finishedTimelinessRate=" + getFinishedTimelinessRate() + ", riskCount=" + getRiskCount() + ")";
    }

    public ProjectMemberVO() {
    }

    public ProjectMemberVO(final String userId, final String userName, final String roleName, final String createTime, final Double workingHour, final Integer taskCount, final Integer bugCount, final Double finishedTimelinessRate, final Integer riskCount) {
        this.userId = userId;
        this.userName = userName;
        this.roleName = roleName;
        this.createTime = createTime;
        this.workingHour = workingHour;
        this.taskCount = taskCount;
        this.bugCount = bugCount;
        this.finishedTimelinessRate = finishedTimelinessRate;
        this.riskCount = riskCount;
    }

    public String getUserId() {
        return this.userId;
    }

    public String getUserName() {
        return this.userName;
    }

    public String getRoleName() {
        return this.roleName;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public Double getWorkingHour() {
        return this.workingHour;
    }

    public Integer getTaskCount() {
        return this.taskCount;
    }

    public Integer getBugCount() {
        return this.bugCount;
    }

    public Double getFinishedTimelinessRate() {
        return this.finishedTimelinessRate;
    }

    public Integer getRiskCount() {
        return this.riskCount;
    }

    public ProjectMemberVO(String userId, Double workingHour) {
        this.userId = userId;
        this.workingHour = workingHour;
    }
}
