package cn.harmonycloud.issue.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.issue.model.*;
import cn.harmonycloud.issue.model.dto.v2.IssuesDTO;
import cn.harmonycloud.issue.model.dto.v2.IssuesQueryDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "track-issues", path = "/provider/track/issues", url = "${track-issues.url:http://localhost:8081/}")
/* loaded from: IssuesProvider.class */
public interface IssuesProvider {
    @PostMapping({"/count"})
    @ApiOperation("查询事项数量")
    BaseResult<Long> getCountByQuery(@Valid @RequestBody IssuesQueryDTO queryDTO);

    @PostMapping({"/page"})
    @ApiOperation("分页查询事项列表")
    BaseResult<Page<IssuesDTO>> issuesListByPage(@Valid @RequestBody IssuesQueryDTO query);

    @PostMapping({"/page/base"})
    @ApiOperation("分页查询事项列表（基表数据）")
    BaseResult<Page<IssuesDTO>> baseIssuesPage(@Valid @RequestBody IssuesQueryDTO query);

    @PostMapping({"/list"})
    @ApiOperation("根据条件查询事项列表(全部数据)")
    BaseResult<List<IssuesDTO>> getIssuesList(@Valid @RequestBody IssuesQueryDTO query);

    @GetMapping({"/detail/{id}"})
    @ApiOperation("获取事项详情")
    BaseResult<IssuesDTO> detailById(@PathVariable("id") Long id);

    @GetMapping(value = {"/detail/businessType/{businessType}/businessId/{businessId}"}, produces = {"application/json;charset=UTF-8"})
    @ApiOperation("根据业务类型和业务id查询事项详情(前端数据结构)")
    BaseResult<List<IssuesFieldDTO>> detailByBusinessTypeAndId(@PathVariable("businessType") Long businessType, @PathVariable("businessId") Long businessId);

    @GetMapping(value = {"/sys/detail/businessType/{businessType}/businessId/{businessId}"}, produces = {"application/json;charset=UTF-8"})
    @ApiOperation("根据业务类型和业务id查询事项详情")
    BaseResult<IssuesDTO> sysDetailByBusinessTypeAndId(@PathVariable("businessType") Long businessType, @PathVariable("businessId") Long businessId);

    @GetMapping({"/detail/{id}/web"})
    @ApiOperation("获取事项详情（前端数据结构）")
    BaseResult<List<IssuesFieldDTO>> detailByIdOfWeb(@PathVariable("id") Long id);

    @PostMapping({"/sys/add"})
    @ApiOperation("新建事项/新建子事项(系统调用)")
    BaseResult<Long> addIssuesOfSys(@Valid @RequestBody IssuesDTO issuesDTO, @RequestHeader(value = "Amp-Organ-Id", required = false) String orgId);

    @PostMapping({"/sys/add/batch"})
    @ApiOperation("批量新建事项/新建子事项(系统调用)")
    BaseResult batchAddIssuesOfSys(@Valid @RequestBody List<IssuesDTO> issuesDTOList, @RequestHeader(value = "Amp-Organ-Id", required = false) String orgId);

    @PostMapping({"/web/add"})
    @ApiOperation("新建事项/新建子事项（前端数据结构）")
    BaseResult<Long> addIssuesOfWeb(@Valid @RequestBody IssuesUpdateDTO issuesDTO);

    @PostMapping({"/sys/update/{id}"})
    @ApiOperation("更新事项(系统调用)")
    BaseResult<Boolean> updateIssuesOfSys(@PathVariable("id") Long id, @RequestBody IssuesDTO issuesDTO);

    @PostMapping({"/web/update/{id}"})
    @ApiOperation("更新事项（前端调用）")
    BaseResult<Long> updateIssuesOfWeb(@PathVariable("id") Long id, @RequestBody IssuesUpdateDTO issuesDTO);

    @PostMapping({"/delete/{id}"})
    @ApiOperation("删除事项")
    BaseResult<Long> delete(@PathVariable("id") Long id, @RequestParam(value = "businessType",required = false) Long businessType, @RequestParam(value = "businessId",required = false) Long businessId);

    @ApiImplicitParams({@ApiImplicitParam(name = "isCustomFields", value = "是否需要扩展字段", defaultValue = "false"), @ApiImplicitParam(name = "businessType", value = "业务类型id"), @ApiImplicitParam(name = "businessId", value = "业务实例id")})
    @GetMapping({"/{id}/children"})
    @ApiOperation("根据事项id/业务类型和业务id获取子事项列表")
    BaseResult<List<IssuesDTO>> getChildrenById(@PathVariable("id") Long id, @RequestParam(value = "isCustomFields",required = false) Boolean isCustomFields, @RequestParam(value = "businessType",required = false) Long businessType, @RequestParam(value = "businessId",required = false) Long businessId);

    @PostMapping({"/{id}/children/{childId}/delete"})
    @ApiImplicitParams({@ApiImplicitParam(name = "businessType", value = "业务类型id"), @ApiImplicitParam(name = "businessId", value = "业务实例id")})
    @ApiOperation("删除子事项")
    BaseResult<Boolean> deleteChildren(@PathVariable("id") Long id, @PathVariable("childId") Long childId, @RequestParam(value = "businessType",required = false) Long businessType, @RequestParam(value = "businessId",required = false) Long businessId);

    @ApiImplicitParams({@ApiImplicitParam(name = "businessType", value = "业务类型id"), @ApiImplicitParam(name = "businessId", value = "业务实例id")})
    @GetMapping({"/{id}/relatives"})
    @ApiOperation("根据事项id/业务类型和业务id获取关联事项列表")
    BaseResult<List<IssuesDTO>> getRelativesById(@PathVariable("id") Long id, @RequestParam(value = "businessType",required = false) Long businessType, @RequestParam(value = "businessId",required = false) Long businessId);

    @PostMapping({"/{id}/relatives/{relativesId}"})
    @ApiImplicitParams({@ApiImplicitParam(name = "businessType", value = "被关联业务类型id"), @ApiImplicitParam(name = "businessId", value = "被关联业务实例id"), @ApiImplicitParam(name = "relativesBusinessType", value = "关联业务类型id"), @ApiImplicitParam(name = "relativesBusinessId", value = "关联业务实例id")})
    @ApiOperation("关联事项")
    BaseResult<Boolean> addRelatives(@PathVariable("id") Long id, @RequestParam(value = "businessType",required = false) Long businessType, @RequestParam(value = "businessId",required = false) Long businessId, @PathVariable("relativesId") Long relativesId, @RequestParam(value = "relativesBusinessType",required = false) Long relativesBusinessType, @RequestParam(value = "relativesBusinessId",required = false) Long relativesBusinessId);

    @PostMapping({"/{id}/relatives/{relativesId}/delete"})
    @ApiImplicitParams({@ApiImplicitParam(name = "businessType", value = "被关联业务类型id"), @ApiImplicitParam(name = "businessId", value = "被关联业务实例id"), @ApiImplicitParam(name = "relativesBusinessType", value = "关联业务类型id"), @ApiImplicitParam(name = "relativesBusinessId", value = "关联业务实例id")})
    @ApiOperation("移除关联关系")
    BaseResult<Boolean> removeRelatives(@PathVariable("id") Long id, @RequestParam(value = "businessType",required = false) Long businessType, @RequestParam(value = "businessId",required = false) Long businessId, @PathVariable("relativesId") Long relativesId, @RequestParam(value = "relativesBusinessType",required = false) Long relativesBusinessType, @RequestParam(value = "relativesBusinessId",required = false) Long relativesBusinessId);

    @GetMapping({"/updateBybusinessTypeAndId"})
    @ApiOperation("根据业务类型和业务id更新事项详情")
    BaseResult<Boolean> updateIssuesByBusinessTypeAndId(@RequestBody IssuesDTO issuesDTO);

    @PostMapping(value = {"/listOfMap"}, produces = {"application/json;charset=UTF-8"})
    @ApiOperation("根据条件查询事项列表(全部数据不分页，返回字段为key:value)")
    BaseResult<List<Map<String, Object>>> getIssuesListOfMap(@RequestBody IssuesQueryDTO query);

    @GetMapping({"/detailOfMap/{id}"})
    @ApiOperation("获取事项详情")
    BaseResult<Map<String, Object>> detailOfMapById(@PathVariable("id") Long id);

    @PostMapping({"/list/base"})
    @ApiOperation("根据条件查询事项列表(基表数据)")
    BaseResult<List<IssuesDTO>> getBaseIssuesList(@Valid @RequestBody IssuesQueryDTO query);

    @PostMapping({"/update/batch"})
    @ApiOperation("根据条件批量更新数据")
    BaseResult<Boolean> batchUpdateIssues(@Valid @RequestBody IssuesDTO issuesDTO);

    @PostMapping({"/statistics"})
    @ApiOperation("统计事项的完成情况")
    BaseResult<List<StatisticsDTO>> statistics(@RequestBody StatisticsQuery statisticsQuery);

    @PostMapping({"/delete/batch"})
    @ApiOperation("根据条件批量删除数据")
    BaseResult<Boolean> batchDeleteIssues(@Valid @RequestBody IssuesQueryDTO issuesDTO);

    @GetMapping({"/projects"})
    @ApiOperation("根据事项id获取所属项目和共享项目ids")
    BaseResult<List<Long>> getProjectIdsByIssuesIdAndType(@RequestParam("issuesId") Long issuesId, @RequestParam(value = "type",required = false) Integer type);

    @PostMapping({"/copy"})
    @ApiOperation("事项管理-复制事项")
    BaseResult<Long> copy(@Valid @RequestBody IssuesCopyDTO issuesCopyDTO);

    @PostMapping({"/original/values"})
    @ApiOperation("事项管理-根据事项ids获取字段的原始属性值")
    BaseResult<Map<String, Object>> getOriginalValueByIssuesId(@RequestParam("issuesId") Long issuesId);

    @PostMapping({"/watchers/add"})
    @ApiOperation("添加关注人")
    BaseResult<Boolean> addWatchers(@RequestBody WatchersDTO watchersDTO);

    @PostMapping({"/watchers/update"})
    @ApiOperation("更新关注人")
    BaseResult<Boolean> updateWatchers(@RequestBody WatchersDTO watchersDTO);

    @PostMapping({"/comments/add"})
    @ApiOperation("添加评论")
    BaseResult<Long> addComments(@RequestBody CommentDTO commentDTO);

    @PostMapping({"/comments/query"})
    @ApiOperation("查找评论")
    BaseResult<List<CommentDTO>> queryComments(@RequestBody CommentDTO commentDTO);

    @PostMapping({"/logs/add"})
    @ApiOperation("添加操作日志:仅做数据迁移用")
    BaseResult<Boolean> addLogs(@RequestBody List<LogsDTO> logsDTOList);

    @PostMapping({"/listByIds"})
    @ApiOperation("根据ids查询事项列表(全部基表数据)")
    BaseResult<List<IssuesDTO>> getListByIds(@Valid @RequestBody IssuesQueryDTO query);

    @PostMapping({"/handover"})
    @ApiOperation("用户移出项目事项交接")
    BaseResult handover(@RequestBody ProjectMemberHandoverDTO handoverBO);
}
