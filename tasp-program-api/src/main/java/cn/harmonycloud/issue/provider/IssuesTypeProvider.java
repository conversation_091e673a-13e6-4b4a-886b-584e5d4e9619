package cn.harmonycloud.issue.provider;

import cn.harmonycloud.common.core.base.BaseResult;

import cn.harmonycloud.issue.model.IssuesTypeCustomFieldDTO;
import cn.harmonycloud.issue.model.IssuesTypeDTO;
import cn.harmonycloud.issue.model.IssuesTypeStatusDTO;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "track-issues-types", path = "/provider/track/issues/types", url = "${track-issues.url:http://localhost:8081/}")
public interface IssuesTypeProvider {
    @GetMapping({"/detail/{code}"})
    @ApiOperation("根据事项类型code获取事项类型详情")
    BaseResult<IssuesTypeDTO> detailByCode(@PathVariable("code") String code);

    @GetMapping({"/{id}/customFields"})
    @ApiOperation("根据事项类型id查询:属性配置列表/事项列表表头/表单字段/详情页字段/系统字段或非系统字段")
    BaseResult<List<IssuesTypeCustomFieldDTO>> listCustomFieldsByIssuesTypeId(@RequestHeader("Amp-Organ-Id") String orgId, @RequestHeader("Authorization") String token, @PathVariable("id") Long id, IssuesTypeCustomFieldDTO query);

    @GetMapping({"/list/{issuesTypeId}"})
    @ApiOperation("根据事项类型id获取所有状态类型")
    BaseResult<List<IssuesTypeStatusDTO>> listByIssuesTypeId(@PathVariable("issuesTypeId") Long issuesTypeId);
}
