package cn.harmonycloud.exception;

import cn.harmonycloud.enums.ExceptionCode;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/25 5:28 下午
 **/
public class SystemException extends RuntimeException{

    private static final long serialVersionUID = 1480870496001690285L;

    private final ExceptionCode exceptionCode;
    private int code = 0;
    private String exMsg;

    public SystemException(ExceptionCode exceptionCode){
        super(exceptionCode.getMsg());
        this.exceptionCode = exceptionCode;
    }

    public SystemException(ExceptionCode exceptionCode, String msg){
        super(msg);
        this.exceptionCode = exceptionCode;
        this.exMsg = msg;
    }

    public SystemException(ExceptionCode exceptionCode,int code, String msg){
        super(msg);
        this.code = code;
        this.exceptionCode = exceptionCode;
        this.exMsg = msg;
    }

    public ExceptionCode getExceptionCode() {
        return exceptionCode;
    }

    public String getExMsg() {
        return exMsg;
    }

    public void setExMsg(String exMsg) {
        this.exMsg = exMsg;
    }

    public int getCode() {
        return code;
    }

}
