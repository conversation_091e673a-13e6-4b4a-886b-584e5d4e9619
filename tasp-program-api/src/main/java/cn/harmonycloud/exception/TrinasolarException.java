package cn.harmonycloud.exception;

import cn.harmonycloud.enums.ExceptionCode;
import lombok.Getter;


/**
 * <AUTHOR>
 */

@Getter
public class TrinasolarException extends RuntimeException {

    private static final long serialVersionUID = 1480870496001690285L;

    private final ExceptionCode exceptionCode;
    private int code = 0;
    private String exMsg;

    public TrinasolarException(ExceptionCode exceptionCode) {
        super(exceptionCode.getMsg());
        this.exceptionCode = exceptionCode;
    }

    public TrinasolarException(ExceptionCode exceptionCode, String msg) {
        super(msg);
        this.exceptionCode = exceptionCode;
        this.exMsg = msg;
    }

    public TrinasolarException(ExceptionCode exceptionCode, int code, String msg) {
        super(msg);
        this.code = code;
        this.exceptionCode = exceptionCode;
        this.exMsg = msg;
    }

}
