package cn.harmonycloud.project.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/* loaded from: devops-project-svc-api-1.0.0.jar:cn/harmonycloud/model/dto/IssuesQueryDTO.class */
public class IssuesQueryDTO {
    @ApiModelProperty("当前页")
    private Integer pageNo = 1;
    @ApiModelProperty("分页单位")
    private Integer pageSize = 10;
    @ApiModelProperty("排除工作项ids")
    private List<Long> excludeIdList;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项ids")
    private List<Long> idList;
    @ApiModelProperty("项目ids")
    private List<Long> projectIdList;
    @ApiModelProperty("负责人ids")
    private List<Long> principalIdList;
    @ApiModelProperty("工作项类型")
    private List<Long> issuesClassicIdList;

    public void setPageNo(final Integer pageNo) {
        this.pageNo = pageNo;
    }

    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setExcludeIdList(final List<Long> excludeIdList) {
        this.excludeIdList = excludeIdList;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setIdList(final List<Long> idList) {
        this.idList = idList;
    }

    public void setProjectIdList(final List<Long> projectIdList) {
        this.projectIdList = projectIdList;
    }

    public void setPrincipalIdList(final List<Long> principalIdList) {
        this.principalIdList = principalIdList;
    }

    public void setIssuesClassicIdList(final List<Long> issuesClassicIdList) {
        this.issuesClassicIdList = issuesClassicIdList;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesQueryDTO) {
            IssuesQueryDTO other = (IssuesQueryDTO) o;
            if (other.canEqual(this)) {
                Object this$pageNo = getPageNo();
                Object other$pageNo = other.getPageNo();
                if (this$pageNo == null) {
                    if (other$pageNo != null) {
                        return false;
                    }
                } else if (!this$pageNo.equals(other$pageNo)) {
                    return false;
                }
                Object this$pageSize = getPageSize();
                Object other$pageSize = other.getPageSize();
                if (this$pageSize == null) {
                    if (other$pageSize != null) {
                        return false;
                    }
                } else if (!this$pageSize.equals(other$pageSize)) {
                    return false;
                }
                Object this$excludeIdList = getExcludeIdList();
                Object other$excludeIdList = other.getExcludeIdList();
                if (this$excludeIdList == null) {
                    if (other$excludeIdList != null) {
                        return false;
                    }
                } else if (!this$excludeIdList.equals(other$excludeIdList)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$idList = getIdList();
                Object other$idList = other.getIdList();
                if (this$idList == null) {
                    if (other$idList != null) {
                        return false;
                    }
                } else if (!this$idList.equals(other$idList)) {
                    return false;
                }
                Object this$projectIdList = getProjectIdList();
                Object other$projectIdList = other.getProjectIdList();
                if (this$projectIdList == null) {
                    if (other$projectIdList != null) {
                        return false;
                    }
                } else if (!this$projectIdList.equals(other$projectIdList)) {
                    return false;
                }
                Object this$principalIdList = getPrincipalIdList();
                Object other$principalIdList = other.getPrincipalIdList();
                if (this$principalIdList == null) {
                    if (other$principalIdList != null) {
                        return false;
                    }
                } else if (!this$principalIdList.equals(other$principalIdList)) {
                    return false;
                }
                Object this$issuesClassicIdList = getIssuesClassicIdList();
                Object other$issuesClassicIdList = other.getIssuesClassicIdList();
                return this$issuesClassicIdList == null ? other$issuesClassicIdList == null : this$issuesClassicIdList.equals(other$issuesClassicIdList);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesQueryDTO;
    }

    public int hashCode() {
        Object $pageNo = getPageNo();
        int result = (1 * 59) + ($pageNo == null ? 43 : $pageNo.hashCode());
        Object $pageSize = getPageSize();
        int result2 = (result * 59) + ($pageSize == null ? 43 : $pageSize.hashCode());
        Object $excludeIdList = getExcludeIdList();
        int result3 = (result2 * 59) + ($excludeIdList == null ? 43 : $excludeIdList.hashCode());
        Object $name = getName();
        int result4 = (result3 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $idList = getIdList();
        int result5 = (result4 * 59) + ($idList == null ? 43 : $idList.hashCode());
        Object $projectIdList = getProjectIdList();
        int result6 = (result5 * 59) + ($projectIdList == null ? 43 : $projectIdList.hashCode());
        Object $principalIdList = getPrincipalIdList();
        int result7 = (result6 * 59) + ($principalIdList == null ? 43 : $principalIdList.hashCode());
        Object $issuesClassicIdList = getIssuesClassicIdList();
        return (result7 * 59) + ($issuesClassicIdList == null ? 43 : $issuesClassicIdList.hashCode());
    }

    public String toString() {
        return "IssuesQueryDTO(pageNo=" + getPageNo() + ", pageSize=" + getPageSize() + ", excludeIdList=" + getExcludeIdList() + ", name=" + getName() + ", idList=" + getIdList() + ", projectIdList=" + getProjectIdList() + ", principalIdList=" + getPrincipalIdList() + ", issuesClassicIdList=" + getIssuesClassicIdList() + ")";
    }

    public Integer getPageNo() {
        return this.pageNo;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public List<Long> getExcludeIdList() {
        return this.excludeIdList;
    }

    public String getName() {
        return this.name;
    }

    public List<Long> getIdList() {
        return this.idList;
    }

    public List<Long> getProjectIdList() {
        return this.projectIdList;
    }

    public List<Long> getPrincipalIdList() {
        return this.principalIdList;
    }

    public List<Long> getIssuesClassicIdList() {
        return this.issuesClassicIdList;
    }
}
