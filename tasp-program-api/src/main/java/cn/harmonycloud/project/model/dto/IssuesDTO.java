package cn.harmonycloud.project.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/* loaded from: devops-project-svc-api-1.0.0.jar:cn/harmonycloud/model/dto/IssuesDTO.class */
public class IssuesDTO {
    @ApiModelProperty("工作项id")
    private Long id;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("工作项编号")
    private String code;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项类型id")
    private Long issuesClassicId;
    @ApiModelProperty("工作项类型名称")
    private String issuesClassicName;
    @ApiModelProperty("状态名称")
    private String status;
    @ApiModelProperty("迭代名称")
    private String sprint;
    @ApiModelProperty("负责人")
    private List<UserDTO> ownerList;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectId(final Long projectId) {
        this.projectId = projectId;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setIssuesClassicId(final Long issuesClassicId) {
        this.issuesClassicId = issuesClassicId;
    }

    public void setIssuesClassicName(final String issuesClassicName) {
        this.issuesClassicName = issuesClassicName;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public void setSprint(final String sprint) {
        this.sprint = sprint;
    }

    public void setOwnerList(final List<UserDTO> ownerList) {
        this.ownerList = ownerList;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof IssuesDTO) {
            IssuesDTO other = (IssuesDTO) o;
            if (other.canEqual(this)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$projectId = getProjectId();
                Object other$projectId = other.getProjectId();
                if (this$projectId == null) {
                    if (other$projectId != null) {
                        return false;
                    }
                } else if (!this$projectId.equals(other$projectId)) {
                    return false;
                }
                Object this$issuesClassicId = getIssuesClassicId();
                Object other$issuesClassicId = other.getIssuesClassicId();
                if (this$issuesClassicId == null) {
                    if (other$issuesClassicId != null) {
                        return false;
                    }
                } else if (!this$issuesClassicId.equals(other$issuesClassicId)) {
                    return false;
                }
                Object this$code = getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$issuesClassicName = getIssuesClassicName();
                Object other$issuesClassicName = other.getIssuesClassicName();
                if (this$issuesClassicName == null) {
                    if (other$issuesClassicName != null) {
                        return false;
                    }
                } else if (!this$issuesClassicName.equals(other$issuesClassicName)) {
                    return false;
                }
                Object this$status = getStatus();
                Object other$status = other.getStatus();
                if (this$status == null) {
                    if (other$status != null) {
                        return false;
                    }
                } else if (!this$status.equals(other$status)) {
                    return false;
                }
                Object this$sprint = getSprint();
                Object other$sprint = other.getSprint();
                if (this$sprint == null) {
                    if (other$sprint != null) {
                        return false;
                    }
                } else if (!this$sprint.equals(other$sprint)) {
                    return false;
                }
                Object this$ownerList = getOwnerList();
                Object other$ownerList = other.getOwnerList();
                return this$ownerList == null ? other$ownerList == null : this$ownerList.equals(other$ownerList);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof IssuesDTO;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $projectId = getProjectId();
        int result2 = (result * 59) + ($projectId == null ? 43 : $projectId.hashCode());
        Object $issuesClassicId = getIssuesClassicId();
        int result3 = (result2 * 59) + ($issuesClassicId == null ? 43 : $issuesClassicId.hashCode());
        Object $code = getCode();
        int result4 = (result3 * 59) + ($code == null ? 43 : $code.hashCode());
        Object $name = getName();
        int result5 = (result4 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $issuesClassicName = getIssuesClassicName();
        int result6 = (result5 * 59) + ($issuesClassicName == null ? 43 : $issuesClassicName.hashCode());
        Object $status = getStatus();
        int result7 = (result6 * 59) + ($status == null ? 43 : $status.hashCode());
        Object $sprint = getSprint();
        int result8 = (result7 * 59) + ($sprint == null ? 43 : $sprint.hashCode());
        Object $ownerList = getOwnerList();
        return (result8 * 59) + ($ownerList == null ? 43 : $ownerList.hashCode());
    }

    public String toString() {
        return "IssuesDTO(id=" + getId() + ", projectId=" + getProjectId() + ", code=" + getCode() + ", name=" + getName() + ", issuesClassicId=" + getIssuesClassicId() + ", issuesClassicName=" + getIssuesClassicName() + ", status=" + getStatus() + ", sprint=" + getSprint() + ", ownerList=" + getOwnerList() + ")";
    }

    public Long getId() {
        return this.id;
    }

    public Long getProjectId() {
        return this.projectId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Long getIssuesClassicId() {
        return this.issuesClassicId;
    }

    public String getIssuesClassicName() {
        return this.issuesClassicName;
    }

    public String getStatus() {
        return this.status;
    }

    public String getSprint() {
        return this.sprint;
    }

    public List<UserDTO> getOwnerList() {
        return this.ownerList;
    }
}
