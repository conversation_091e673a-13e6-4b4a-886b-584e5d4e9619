package cn.harmonycloud.project.model.dto;

/* loaded from: devops-project-svc-api-1.0.0.jar:cn/harmonycloud/model/dto/UserDTO.class */
public class UserDTO {
    private String userId1;
    private String userName;

    public UserDTO() {
    }

    public void setUserId1(final String userId1) {
        this.userId1 = userId1;
    }

    public void setUserName(final String userName) {
        this.userName = userName;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof UserDTO) {
            UserDTO other = (UserDTO) o;
            if (other.canEqual(this)) {
                Object this$userId1 = getUserId1();
                Object other$userId1 = other.getUserId1();
                if (this$userId1 == null) {
                    if (other$userId1 != null) {
                        return false;
                    }
                } else if (!this$userId1.equals(other$userId1)) {
                    return false;
                }
                Object this$userName = getUserName();
                Object other$userName = other.getUserName();
                return this$userName == null ? other$userName == null : this$userName.equals(other$userName);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof UserDTO;
    }

    public int hashCode() {
        Object $userId1 = getUserId1();
        int result = (1 * 59) + ($userId1 == null ? 43 : $userId1.hashCode());
        Object $userName = getUserName();
        return (result * 59) + ($userName == null ? 43 : $userName.hashCode());
    }

    public String toString() {
        return "UserDTO(userId1=" + getUserId1() + ", userName=" + getUserName() + ")";
    }

    public String getUserId1() {
        return this.userId1;
    }

    public String getUserName() {
        return this.userName;
    }

    public UserDTO(String userId, String userName) {
        this.userId1 = userId;
        this.userName = userName;
    }
}
