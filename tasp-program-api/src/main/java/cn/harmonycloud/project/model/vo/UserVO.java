package cn.harmonycloud.project.model.vo;

/* loaded from: devops-project-svc-api-1.0.0.jar:cn/harmonycloud/model/vo/UserVO.class */
public class UserVO {
    private String userId;
    private String userName;
    private String roleName;
    private String departName;

    public void setUserId(final String userId) {
        this.userId = userId;
    }

    public void setUserName(final String userName) {
        this.userName = userName;
    }

    public void setRoleName(final String roleName) {
        this.roleName = roleName;
    }

    public void setDepartName(final String departName) {
        this.departName = departName;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof UserVO) {
            UserVO other = (UserVO) o;
            if (other.canEqual(this)) {
                Object this$userId = getUserId();
                Object other$userId = other.getUserId();
                if (this$userId == null) {
                    if (other$userId != null) {
                        return false;
                    }
                } else if (!this$userId.equals(other$userId)) {
                    return false;
                }
                Object this$userName = getUserName();
                Object other$userName = other.getUserName();
                if (this$userName == null) {
                    if (other$userName != null) {
                        return false;
                    }
                } else if (!this$userName.equals(other$userName)) {
                    return false;
                }
                Object this$roleName = getRoleName();
                Object other$roleName = other.getRoleName();
                if (this$roleName == null) {
                    if (other$roleName != null) {
                        return false;
                    }
                } else if (!this$roleName.equals(other$roleName)) {
                    return false;
                }
                Object this$departName = getDepartName();
                Object other$departName = other.getDepartName();
                return this$departName == null ? other$departName == null : this$departName.equals(other$departName);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof UserVO;
    }

    public int hashCode() {
        Object $userId = getUserId();
        int result = (1 * 59) + ($userId == null ? 43 : $userId.hashCode());
        Object $userName = getUserName();
        int result2 = (result * 59) + ($userName == null ? 43 : $userName.hashCode());
        Object $roleName = getRoleName();
        int result3 = (result2 * 59) + ($roleName == null ? 43 : $roleName.hashCode());
        Object $departName = getDepartName();
        return (result3 * 59) + ($departName == null ? 43 : $departName.hashCode());
    }

    public String toString() {
        return "UserVO(userId=" + getUserId() + ", userName=" + getUserName() + ", roleName=" + getRoleName() + ", departName=" + getDepartName() + ")";
    }

    public String getUserId() {
        return this.userId;
    }

    public String getUserName() {
        return this.userName;
    }

    public String getRoleName() {
        return this.roleName;
    }

    public String getDepartName() {
        return this.departName;
    }
}
