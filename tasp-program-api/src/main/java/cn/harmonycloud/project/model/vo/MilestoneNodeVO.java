package cn.harmonycloud.project.model.vo;

import cn.harmonycloud.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;

@ApiModel(value = "MilestoneNode", description = "里程碑节点")
/* loaded from: devops-project-svc-api-1.0.0.jar:cn/harmonycloud/model/vo/MilestoneNodeVO.class */
public class MilestoneNodeVO extends BaseEntity {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty(name = "里程碑id")
    private Long milestoneId;
    @ApiModelProperty(name = "迭代开始后几天后开始")
    private Integer delayDays;
    @ApiModelProperty(name = "名称")
    private String name;
    @ApiModelProperty(name = "描述")
    private String description;
    @ApiModelProperty(name = "0未完成1已完成")
    private Integer status;
    @ApiModelProperty(name = "实际完成日期")
    private LocalDate actualEndDate;
    @ApiModelProperty(name = "计划完成日期")
    private LocalDate planEndDate;
    @ApiModelProperty(name = "计划开始日期")
    private LocalDate planStartDate;
    @ApiModelProperty(name = "实际开始日期")
    private LocalDate actualStartDate;
    @ApiModelProperty(name = "是否默认，默认的不可修改名称，只能改时间和负责人")
    private Integer isDefault;
    @ApiModelProperty(name = "项目阶段id")
    private Long projectStagePlanId;

    public void setId(final Long id) {
        this.id = id;
    }

    public void setMilestoneId(final Long milestoneId) {
        this.milestoneId = milestoneId;
    }

    public void setDelayDays(final Integer delayDays) {
        this.delayDays = delayDays;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public void setStatus(final Integer status) {
        this.status = status;
    }

    public void setActualEndDate(final LocalDate actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    public void setPlanEndDate(final LocalDate planEndDate) {
        this.planEndDate = planEndDate;
    }

    public void setPlanStartDate(final LocalDate planStartDate) {
        this.planStartDate = planStartDate;
    }

    public void setActualStartDate(final LocalDate actualStartDate) {
        this.actualStartDate = actualStartDate;
    }

    public void setIsDefault(final Integer isDefault) {
        this.isDefault = isDefault;
    }

    public void setProjectStagePlanId(final Long projectStagePlanId) {
        this.projectStagePlanId = projectStagePlanId;
    }

    public String toString() {
        return "MilestoneNodeVO(id=" + getId() + ", milestoneId=" + getMilestoneId() + ", delayDays=" + getDelayDays() + ", name=" + getName() + ", description=" + getDescription() + ", status=" + getStatus() + ", actualEndDate=" + getActualEndDate() + ", planEndDate=" + getPlanEndDate() + ", planStartDate=" + getPlanStartDate() + ", actualStartDate=" + getActualStartDate() + ", isDefault=" + getIsDefault() + ", projectStagePlanId=" + getProjectStagePlanId() + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        }
        if (o instanceof MilestoneNodeVO) {
            MilestoneNodeVO other = (MilestoneNodeVO) o;
            if (other.canEqual(this) && super/*java.lang.Object*/.equals(o)) {
                Object this$id = getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }
                Object this$milestoneId = getMilestoneId();
                Object other$milestoneId = other.getMilestoneId();
                if (this$milestoneId == null) {
                    if (other$milestoneId != null) {
                        return false;
                    }
                } else if (!this$milestoneId.equals(other$milestoneId)) {
                    return false;
                }
                Object this$delayDays = getDelayDays();
                Object other$delayDays = other.getDelayDays();
                if (this$delayDays == null) {
                    if (other$delayDays != null) {
                        return false;
                    }
                } else if (!this$delayDays.equals(other$delayDays)) {
                    return false;
                }
                Object this$status = getStatus();
                Object other$status = other.getStatus();
                if (this$status == null) {
                    if (other$status != null) {
                        return false;
                    }
                } else if (!this$status.equals(other$status)) {
                    return false;
                }
                Object this$isDefault = getIsDefault();
                Object other$isDefault = other.getIsDefault();
                if (this$isDefault == null) {
                    if (other$isDefault != null) {
                        return false;
                    }
                } else if (!this$isDefault.equals(other$isDefault)) {
                    return false;
                }
                Object this$projectStagePlanId = getProjectStagePlanId();
                Object other$projectStagePlanId = other.getProjectStagePlanId();
                if (this$projectStagePlanId == null) {
                    if (other$projectStagePlanId != null) {
                        return false;
                    }
                } else if (!this$projectStagePlanId.equals(other$projectStagePlanId)) {
                    return false;
                }
                Object this$name = getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }
                Object this$description = getDescription();
                Object other$description = other.getDescription();
                if (this$description == null) {
                    if (other$description != null) {
                        return false;
                    }
                } else if (!this$description.equals(other$description)) {
                    return false;
                }
                Object this$actualEndDate = getActualEndDate();
                Object other$actualEndDate = other.getActualEndDate();
                if (this$actualEndDate == null) {
                    if (other$actualEndDate != null) {
                        return false;
                    }
                } else if (!this$actualEndDate.equals(other$actualEndDate)) {
                    return false;
                }
                Object this$planEndDate = getPlanEndDate();
                Object other$planEndDate = other.getPlanEndDate();
                if (this$planEndDate == null) {
                    if (other$planEndDate != null) {
                        return false;
                    }
                } else if (!this$planEndDate.equals(other$planEndDate)) {
                    return false;
                }
                Object this$planStartDate = getPlanStartDate();
                Object other$planStartDate = other.getPlanStartDate();
                if (this$planStartDate == null) {
                    if (other$planStartDate != null) {
                        return false;
                    }
                } else if (!this$planStartDate.equals(other$planStartDate)) {
                    return false;
                }
                Object this$actualStartDate = getActualStartDate();
                Object other$actualStartDate = other.getActualStartDate();
                return this$actualStartDate == null ? other$actualStartDate == null : this$actualStartDate.equals(other$actualStartDate);
            }
            return false;
        }
        return false;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof MilestoneNodeVO;
    }

    public int hashCode() {
        int result = super/*java.lang.Object*/.hashCode();
        Object $id = getId();
        int result2 = (result * 59) + ($id == null ? 43 : $id.hashCode());
        Object $milestoneId = getMilestoneId();
        int result3 = (result2 * 59) + ($milestoneId == null ? 43 : $milestoneId.hashCode());
        Object $delayDays = getDelayDays();
        int result4 = (result3 * 59) + ($delayDays == null ? 43 : $delayDays.hashCode());
        Object $status = getStatus();
        int result5 = (result4 * 59) + ($status == null ? 43 : $status.hashCode());
        Object $isDefault = getIsDefault();
        int result6 = (result5 * 59) + ($isDefault == null ? 43 : $isDefault.hashCode());
        Object $projectStagePlanId = getProjectStagePlanId();
        int result7 = (result6 * 59) + ($projectStagePlanId == null ? 43 : $projectStagePlanId.hashCode());
        Object $name = getName();
        int result8 = (result7 * 59) + ($name == null ? 43 : $name.hashCode());
        Object $description = getDescription();
        int result9 = (result8 * 59) + ($description == null ? 43 : $description.hashCode());
        Object $actualEndDate = getActualEndDate();
        int result10 = (result9 * 59) + ($actualEndDate == null ? 43 : $actualEndDate.hashCode());
        Object $planEndDate = getPlanEndDate();
        int result11 = (result10 * 59) + ($planEndDate == null ? 43 : $planEndDate.hashCode());
        Object $planStartDate = getPlanStartDate();
        int result12 = (result11 * 59) + ($planStartDate == null ? 43 : $planStartDate.hashCode());
        Object $actualStartDate = getActualStartDate();
        return (result12 * 59) + ($actualStartDate == null ? 43 : $actualStartDate.hashCode());
    }

    public Long getId() {
        return this.id;
    }

    public Long getMilestoneId() {
        return this.milestoneId;
    }

    public Integer getDelayDays() {
        return this.delayDays;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public Integer getStatus() {
        return this.status;
    }

    public LocalDate getActualEndDate() {
        return this.actualEndDate;
    }

    public LocalDate getPlanEndDate() {
        return this.planEndDate;
    }

    public LocalDate getPlanStartDate() {
        return this.planStartDate;
    }

    public LocalDate getActualStartDate() {
        return this.actualStartDate;
    }

    public Integer getIsDefault() {
        return this.isDefault;
    }

    public Long getProjectStagePlanId() {
        return this.projectStagePlanId;
    }
}
