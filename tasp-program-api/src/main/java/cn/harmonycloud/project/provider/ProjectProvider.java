package cn.harmonycloud.project.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.project.model.dto.ProjectQueryDTO;
import cn.harmonycloud.project.model.vo.ProjectBaseVO;
import cn.harmonycloud.project.model.vo.ProjectQueryVO;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "projectProvider", path = "/provider/devops/project", url = "${devops-project-svc.url:http://localhost:8080/}")
/* loaded from: devops-project-svc-api-1.0.0.jar:cn/harmonycloud/provider/ProjectProvider.class */
public interface ProjectProvider {
    @GetMapping({"/queryAll"})
    @ApiOperation("查询所有项目")
    BaseResult<List<ProjectQueryVO>> queryAll(@SpringQueryMap ProjectQueryDTO projectQueryDTO);

    @GetMapping({"/{id}"})
    @ApiOperation("根据ID查询项目详情")
    BaseResult<ProjectQueryVO> getById(@PathVariable("id") String id);

    @GetMapping({"/queryBaseList"})
    @ApiOperation("查询所有项目的基本信息")
    BaseResult<List<ProjectQueryVO>> queryBaseList(@SpringQueryMap ProjectQueryDTO projectQueryDTO);

    @GetMapping({"/user"})
    @ApiOperation("模糊查询")
    BaseResult<List<ProjectBaseVO>> getByUser();
}
