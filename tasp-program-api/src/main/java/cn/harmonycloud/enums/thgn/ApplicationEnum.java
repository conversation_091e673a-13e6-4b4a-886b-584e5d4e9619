package cn.harmonycloud.enums.thgn;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/8/2
 */
@AllArgsConstructor
public enum ApplicationEnum {
    /**
     * 应用系统
     */
    APPLICATION_SYSTEM("applicationSystem", "应用系统"),

    /**
     * 应用程序
     */
    APPLICATION_PROGRAM("applicationProgram", "应用程序");

    private String code;

    private String name;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
