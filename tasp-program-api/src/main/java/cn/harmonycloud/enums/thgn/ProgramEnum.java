package cn.harmonycloud.enums.thgn;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/8/2
 */
@AllArgsConstructor
public enum ProgramEnum {
    /**
     * 应用系统
     */
    INIT_COMPLETE("1", "初始化完成"),

    /**
     * 应用程序
     */
    INIT_WAITING("0", "待初始化"),

    /**
     * 异常
     */
    INIT_ERROR("2","初始化异常");

    private String code;

    private String name;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
