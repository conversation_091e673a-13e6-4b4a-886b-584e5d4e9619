package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/9/13
 **/
@AllArgsConstructor
@Getter
public enum TestStatusEnum {

    /**
     * 环境测试状态:待测试=1,测试中=2,测试通过=3,测试失败=4
     */
    TEST_WAIT(1, "待测试"),
    TEST_BEING(2, "测试中"),
    TEST_PASS(3, "测试通过"),
    TEST_FAIL(4, "测试失败"),
    Test_COMPLETE(5, "测试结束");

    private Integer code;

    private String name;

    public static String getTestStatus(Integer code) {
        for (TestStatusEnum value : TestStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
