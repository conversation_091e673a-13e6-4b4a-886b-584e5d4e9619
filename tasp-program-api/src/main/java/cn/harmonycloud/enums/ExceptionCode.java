package cn.harmonycloud.enums;


public enum ExceptionCode {


    INNER_EXCEPTION(500, "内部异常"),
    REQUEST_OAUTH_CHECK_FAIL(401, "登录状态已失效或已过期"),
    REQUEST_METHOD_NOT_ALLOWED(405, "错误的请求方式"),
    REQUEST_MESSAGE_NOT_READ(400, "参数体获取失败"),
    REQUEST_ARGUMENT_NOT_VALID(406, "参数校验异常"),
    DATA_IS_NOT_FOUND(404,"数据不存在"),

    PERMISSION_DENIED(403, "权限校验错误"),

    // 应用内部异常
    THREAD_EXECUTE_FAIL(1,"执行错误"),

    // 本地数据异常
    DATA_IS_ALREADY_EXIT(1001,"数据已存在"),
    DATA_SAVA_OR_UPDATE_FAIL(1002,"数据保存失败"),
    DATA_CONFIG_FAIL(1003,"数据配置异常，请联系管理员"),
    DATA_EXCEPTION_FAIL(1004,"数据异常，请联系管理员"),


    // 远程调用异常
    REMOTE_FAIL(2001,"远程服务异常"),
    REMOTE_SCM_FAIL(2002,"代码服务异常"),
    REMOTE_REPOSITORY_FAIL(2003,"制品库服务异常"),
    REMOTE_KUBERNETES_FAIL(2004,"k8s服务异常"),
    REMOTE_APP_MANAGE_FAIL(2005,"应用管理服务异常"),
    REMOTE_PIPELINE_FAIL(2006,"流水线服务异常"),
    REMOTE_ISSUES_FAIL(2007,"跟踪事项服务异常"),
    REMOTE_PROJECT_FAIL(2008,"项目管理服务异常"),

    // 业务异常
    CREATE_BRANCH_FAIL(3001, "分支创建失败"),
    CREATE_MERGE_REQUEST_FAIL(3002, "合并请求创建失败"),
    CREATE_MERGE_TASK_FAIL(3003, "分支创建失败"),
    OPERATION_FAIL(3004, "初始化失败"),
    VERSION_UPDATE_FILE(3005, "版本更新失败"),
    SYSTEM_MEMBER_FILE(3006, "成员操作失败"),
    JOB_EXECUTE_FAIL(3007, "流水线构建失败"),
    CREATE_REPOSITORY_FAIL(3008, "仓库创建异常"),
    CLEAR_BRANCH_FAIL(3009, "分支清理异常"),
    CLEAR_BRANCH_CHECK_MERGE(3010, "分支清理校验异常"),
    SUBSYSTEM_MEMBER_FILE(3011, "子系统成员操作失败"),

    // 通用异常
    PROMPT(5001, "错误提示"),
    ;


    private int code;
    private String msg;
    private String module;

    ExceptionCode(int code, String msg){
        this.code = code;
        this.msg = msg;
        this.module = "";
    }


    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getModule() {
        return module;
    }

}
