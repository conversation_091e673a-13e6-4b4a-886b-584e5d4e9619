package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProjectTypeEnum {
    P1(1, "甲类"),
    P2(2, "乙类"),
    P3(3, "丙类"),
    P4(4, "运维工作"),
    P5(5, "硬件类项目"),
    P6(6, "系统科项目"),
    P7(7, "安全科项目"),
    P8(8, "外部项目"),
    P9(9, "迭代项目"),
    P10(10, "任务类项目"),
    P11(11, "内部技术研发");

    private Integer id;
    private String desc;

    public static ProjectTypeEnum getEnum(Integer id, String desc) {
        for(ProjectTypeEnum item : ProjectTypeEnum.values()) {
            if(item.getId().equals(id)) {
                return item;
            }
            if(item.getDesc().equals(desc)) {
                return item;
            }
        }
        return null;
    }
}
