package cn.harmonycloud.enums;

import lombok.Getter;

/**
 * @ClassName EnvVarInitEnum
 * @Description
 * <AUTHOR>
 * @Date 2022/9/29 11:25 AM
 **/
public enum EnvVarInitEnum {

    ORDINARY_ARTIFACT("ordinaryArtifact","zip,tar.gz","普通制品"),
    CONTAINER_ARTIFACT("containerArtifact", "yaml", "容器制品"),
    NEXUS_CODE_NAME("nexusCodeName",null,"nexus地址和子系统仓库目录"),
    HARBOR_PROJECT_NAME("harborProjectName",null,"harbor地址和项目目录"),
    UPDATE_REGION("updateRegion", null, "更新区域");

    public String key;
    public String value;
    public String description;

    EnvVarInitEnum(String key,String value,String description){
        this.key = key;
        this.value = value;
        this.description = description;
    }
}
