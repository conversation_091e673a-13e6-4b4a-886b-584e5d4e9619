package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProjectStatusEnum {
    ON_GOING(0, "进行中"),
    COMPLETE(1, "已完成");

    private Integer id;
    private String desc;

    public static ProjectStatusEnum getEnum(Integer id, String desc) {
        for(ProjectStatusEnum item : ProjectStatusEnum.values()) {
            if(item.getId().equals(id)) return item;
            if(item.getDesc().equals(desc)) return item;
        }
        return null;
    }

}
