package cn.harmonycloud.enums;


import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SubSystemComponentEnum {

    /**
     * 代码仓库
     */
    GITLAB("GITLAB", "id"),
    CONFIG("CONFIG", "id");


    private String component;

    private String keyType;

    public String getComponent() {
        return this.component;
    }

    public String getKeyType() {
        return this.keyType;
    }


}
