package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProjectAddTypeEnum {

    CREATE(1, "手动创建"),
    IMPORT(2, "导入");

    private Integer id;
    private String desc;

    public static ProjectAddTypeEnum getEnum(Integer id, String desc) {
        for(ProjectAddTypeEnum item : ProjectAddTypeEnum.values()) {
            if(item.getDesc().equals(desc)) return item;
            if(item.getId().equals(id)) return item;
        }
        return null;
    }
}
