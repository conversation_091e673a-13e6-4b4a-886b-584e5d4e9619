package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/8/4
 */
@AllArgsConstructor
public enum ContextEnum {
    /**
     * sit
     */
    SIT("sit", "sit环境"),

    /**
     * uat
     */
    UAT("uat", "uat环境"),

    /**
     * main
     */
    main("main", "main环境");

    private String code;

    private String name;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
