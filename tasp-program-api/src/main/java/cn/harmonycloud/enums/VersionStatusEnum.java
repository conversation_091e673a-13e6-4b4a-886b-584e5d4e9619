package cn.harmonycloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/28 9:31 上午
 **/
@AllArgsConstructor
@Getter
public enum VersionStatusEnum {

    /**
     * 环境测试状态:待测试=1,测试中=2,测试通过=3,测试失败=4
     */
    NOT_START(0, "未开始"),
    DEVELOP(1, "开发中"),
    TESTING(2, "测试中"),
    DEPLOY(3, "已发布");

    private Integer code;

    private String name;

    public static String getStatus(Integer code) {
        for (VersionStatusEnum value : VersionStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
