package cn.harmonycloud.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/21 9:55 上午
 **/
@Data
public class BuildInstanceDto implements Serializable {

    private static final long serialVersionUID = 5880314793171048068L;

    private Long buildId;
    private Long versionId;
    private List<FeatureInfoDto> features = new ArrayList<>();
    private List<TestManageDto> testManagements = new ArrayList<>();

}
