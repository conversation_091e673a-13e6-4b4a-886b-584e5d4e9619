package cn.harmonycloud.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/21 1:46 下午
 **/
@Data
public class TestManageDto implements Serializable {

    private Long id;

    /**
     * 测试记录编号
     */
    private String testCode;

    /**
     * 补丁号（拼接环境测试编号后）
     */
    private String patchNumber;

    /**
     * 测试环境
     */
    private String testEnv;

    /**
     * main环境为version_instance_id,其他环境为特性表id
     */
    private String componentIds;

    /**
     * 所属子系统id
     */
    private Long subSystemId;

    /**
     * 所属系统id
     */
    private Long systemId;

    /**
     * 阶段环境id
     */
    private Long stageEnvId;

    /**
     * 构建实例id
     */
    private Long buildInstanceId;

    /**
     * 测试负责人id
     */
    private Long directorId;
}
