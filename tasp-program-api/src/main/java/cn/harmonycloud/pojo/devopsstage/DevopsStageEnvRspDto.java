package cn.harmonycloud.pojo.devopsstage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/5 4:25 下午
 **/
@ApiModel("阶段环境返回对象")
public class DevopsStageEnvRspDto {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("子系统名称")
    private String subsystemName;

    @ApiModelProperty("所属阶段id")
    private Long stageId;

    @ApiModelProperty("所属阶段名称")
    private String stageName = "-";

    @ApiModelProperty("环境名称")
    private String envName;

    @ApiModelProperty("阶段环境编码")
    private String stageEnvCode;

    @ApiModelProperty("部署环境id")
    private Integer envId;

    @ApiModelProperty("部署类型")
    private Integer deployType;

    @ApiModelProperty("部署资源：容器资源")
    private Integer clusterId;

    @ApiModelProperty("集群命名空间")
    private String namespace;

    @ApiModelProperty("部署资源：主机资源")
    private List<Integer> hostId = new ArrayList<>();

    @ApiModelProperty("通用制品库id")
    private Long rawRepoId;

    @ApiModelProperty("docker制品库id")
    private Long containerRepoId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSubsystemId() {
        return subsystemId;
    }

    public void setSubsystemId(Long subsystemId) {
        this.subsystemId = subsystemId;
    }

    public String getSubsystemName() {
        return subsystemName;
    }

    public void setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
    }

    public Long getStageId() {
        return stageId;
    }

    public void setStageId(Long stageId) {
        this.stageId = stageId;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getEnvName() {
        return envName;
    }

    public void setEnvName(String envName) {
        this.envName = envName;
    }

    public String getStageEnvCode() {
        return stageEnvCode;
    }

    public void setStageEnvCode(String stageEnvCode) {
        this.stageEnvCode = stageEnvCode;
    }

    public Integer getEnvId() {
        return envId;
    }

    public void setEnvId(Integer envId) {
        this.envId = envId;
    }

    public Integer getDeployType() {
        return deployType;
    }

    public void setDeployType(Integer deployType) {
        this.deployType = deployType;
    }

    public Integer getClusterId() {
        return clusterId;
    }

    public void setClusterId(Integer clusterId) {
        this.clusterId = clusterId;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public List<Integer> getHostId() {
        return hostId;
    }

    public void setHostId(List<Integer> hostId) {
        this.hostId = hostId;
    }

    public Long getRawRepoId() {
        return rawRepoId;
    }

    public void setRawRepoId(Long rawRepoId) {
        this.rawRepoId = rawRepoId;
    }

    public Long getContainerRepoId() {
        return containerRepoId;
    }

    public void setContainerRepoId(Long containerRepoId) {
        this.containerRepoId = containerRepoId;
    }
}
