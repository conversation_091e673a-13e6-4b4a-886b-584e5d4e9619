package cn.harmonycloud.pojo.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("大版本实体")
public class MajorVersionDto {
    @ApiModelProperty("id主键")
    private Long id;

    @ApiModelProperty("子系统id")
    private Long subSystemId;

    @ApiModelProperty("大版本号")
    private String versionNumber;


    private Integer versionStatus;

    @ApiModelProperty("负责人id")
    private Long directorId;

    @ApiModelProperty("负责人名称")
    private String directorName;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("版本描述")
    private String description;
}
