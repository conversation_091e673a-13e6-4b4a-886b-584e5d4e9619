package cn.harmonycloud.pojo.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/11 4:18 下午
 **/
@Data
@ApiModel("版本实体")
public class VersionDto {

    @ApiModelProperty("id主键")
    private Long id;

    @ApiModelProperty("子系统id")
    private Long subSystemId;

    @ApiModelProperty("大版本id")
    private Long majorVersionId;

    @ApiModelProperty("大版本号")
    private String versionNumber;

    @ApiModelProperty("拼接版本号")
    private String totalVersionNumber;

    @ApiModelProperty("负责人id")
    private Long directorId;

    @ApiModelProperty("负责人名称")
    private String directorName;

    @ApiModelProperty("子版本号")
    private String subVersionNumber;

    @ApiModelProperty("版本号类型 1-日期版本；2-自定义版本")
    private Integer versionType;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建人名称")
    private String createByName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;


    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新人名称")
    private String updateByName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("版本状态：0-未开始；1-开发中；2-测试中；3-已发布")
    private Integer versionStatus;

    @ApiModelProperty("版本开启状态：0-开启；1-未开启")
    private Integer switchStatus;

    @ApiModelProperty("版本描述")
    private String description;

}
