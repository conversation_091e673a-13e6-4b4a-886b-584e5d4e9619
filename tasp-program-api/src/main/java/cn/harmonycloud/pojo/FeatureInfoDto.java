package cn.harmonycloud.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/21 10:07 上午
 **/
@Data
public class FeatureInfoDto implements Serializable {


    private Long featureId;
    private String featureName;
    private Integer featureStatus;
    private Long projectId;
    private Integer integrationFlag; // 0-否，1-是
    private String projectName;
    private Long directorId;
    private String directorName;
    private String featureCode;
    private String branchName;
    private String devBranch;
    private String branch;

}
