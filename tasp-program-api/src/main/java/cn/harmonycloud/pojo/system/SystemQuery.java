package cn.harmonycloud.pojo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 11:15 上午
 **/
@Data
@ApiModel("系统查询参数")
public class SystemQuery {

    // 优先级高于请求头的租户di
    @ApiModelProperty("租户id")
    private Long organId;

    // 系统id列表
    @ApiModelProperty("id列表")
    private List<Long> ids;
}
