package cn.harmonycloud.pojo.coderepo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/1/4 2:00 下午
 **/
@Data
public class MergeStatisticInfoResponse {

    private Integer gitlabId;
    @ApiModelProperty("我提交的数量")
    private Integer submitCount = 0;
    @ApiModelProperty("我评审的数量")
    private Integer reviewCount = 0;
    @ApiModelProperty("与我相关且当前处于待评审的数量")
    private Integer currentReviewCount = 0;
    @ApiModelProperty("与我相关且当前处于待合并的数量")
    private Integer currentWaitMergeCount = 0;

}
