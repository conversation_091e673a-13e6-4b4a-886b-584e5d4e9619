package cn.harmonycloud.pojo.subsystem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/17 4:34 下午
 **/
@Data
@ApiModel("成员查询模型")
public class MemberQuery implements Serializable {


    private static final long serialVersionUID = -434337050202142724L;

    @ApiModelProperty("实例id")
    @NotEmpty(message = "实例id不能为空")
    private String instanceId;

    @ApiModelProperty("是否携带system管理员")
    private Boolean withSystemAdmin = false;

    public MemberQuery(){

    }

    public MemberQuery(String instanceId, Boolean withSystemAdmin){
        this.instanceId = instanceId;
        this.withSystemAdmin = withSystemAdmin;
    }

}
