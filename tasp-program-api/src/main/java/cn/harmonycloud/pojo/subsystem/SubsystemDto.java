package cn.harmonycloud.pojo.subsystem;

import cn.harmonycloud.pmp.model.entity.RoleBase;
import cn.harmonycloud.pojo.coderepo.MergeStatisticInfoResponse;
import cn.harmonycloud.pojo.version.MajorVersionDto;
import cn.harmonycloud.pojo.version.VersionDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/4 10:08 上午
 **/
@ApiModel("子系统信息")
public class SubsystemDto implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("子系统中文全称")
    private String fullNameCn;

    @ApiModelProperty("子系统编码")
    private String subCode;

    @ApiModelProperty("子系统描述")
    private String subDescCn;

    @ApiModelProperty("系统id")
    private Long systemId;

    @ApiModelProperty("系统名称")
    private String systemName;

    @ApiModelProperty("负责人")
    private Long techDirectorId;

    @ApiModelProperty("负责人")
    private String techDirectorName;

    @ApiModelProperty("语言")
    private String technology;

    @ApiModelProperty("代码库id")
    private Integer gitlabId;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("特性数量")
    private Integer featureNum;

    @ApiModelProperty("合并数量")
    private Long myMergeRequestNum = 0L;

    private MergeStatisticInfoResponse mergeStatisticInfoResponse;

    private List<MajorVersionDto> versions = new ArrayList<>();

    private List<RoleBase> roles = new ArrayList();
;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFullNameCn() {
        return fullNameCn;
    }

    public void setFullNameCn(String fullNameCn) {
        this.fullNameCn = fullNameCn;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubDescCn() {
        return subDescCn;
    }

    public void setSubDescCn(String subDescCn) {
        this.subDescCn = subDescCn;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public Long getTechDirectorId() {
        return techDirectorId;
    }

    public void setTechDirectorId(Long techDirectorId) {
        this.techDirectorId = techDirectorId;
    }

    public String getTechnology() {
        return technology;
    }

    public void setTechnology(String technology) {
        this.technology = technology;
    }

    public Integer getGitlabId() {
        return gitlabId;
    }

    public void setGitlabId(Integer gitlabId) {
        this.gitlabId = gitlabId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getFeatureNum() {
        return featureNum;
    }

    public void setFeatureNum(Integer featureNum) {
        this.featureNum = featureNum;
    }

    public String getTechDirectorName() {
        return techDirectorName;
    }

    public void setTechDirectorName(String techDirectorName) {
        this.techDirectorName = techDirectorName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public List<MajorVersionDto> getVersions() {
        return versions;
    }

    public void setVersions(List<MajorVersionDto> versions) {
        this.versions = versions;
    }

    public List<RoleBase> getRoles() {
        return roles;
    }

    public void setRoles(List<RoleBase> roles) {
        this.roles = roles;
    }

    public Long getMyMergeRequestNum() {
        return myMergeRequestNum;
    }

    public void setMyMergeRequestNum(Long myMergeRequestNum) {
        this.myMergeRequestNum = myMergeRequestNum;
    }

    public MergeStatisticInfoResponse getMergeStatisticInfoResponse() {
        return mergeStatisticInfoResponse;
    }

    public void setMergeStatisticInfoResponse(MergeStatisticInfoResponse mergeStatisticInfoResponse) {
        this.mergeStatisticInfoResponse = mergeStatisticInfoResponse;
    }
}
