package cn.harmonycloud.pojo.subsystem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/12 2:30 下午
 **/
@Data
@ApiModel("应用配置保存")
public class SubsystemConfigSaveRequest {

    @ApiModelProperty("应用id")
    @NotNull(message = "应用id不能为空")
    private Long subsystemId;

    @ApiModelProperty("是否清理临时分支：0-关闭；1-清理")
    private Integer clearTemporaryBranchFlag;

    @ApiModelProperty("清理时间:单位 天")
    private Integer clearTemporaryBranchTime;
}
