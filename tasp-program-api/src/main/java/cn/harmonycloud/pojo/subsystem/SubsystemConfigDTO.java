package cn.harmonycloud.pojo.subsystem;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/12 2:22 下午
 **/
@Data
@ApiModel("应用配置")
public class SubsystemConfigDTO {

    private Long id;

    @ApiModelProperty("应用id")
    private Long subsystemId;

    @ApiModelProperty("是否清理临时分支：0-关闭；1-清理")
    private Integer clearTemporaryBranchFlag;

    @ApiModelProperty("清理时间:单位 天")
    private Integer clearTemporaryBranchTime;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

}
