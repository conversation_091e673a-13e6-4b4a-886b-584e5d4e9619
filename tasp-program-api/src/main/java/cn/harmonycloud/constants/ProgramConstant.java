package cn.harmonycloud.constants;

/**
 * @className: PROGRAMConstant
 * @Description: 应用程序常量
 * @author: pengshy
 * @date: 2025/9/2 14:45
 */
public interface ProgramConstant {
    /**
     * 应用系统变更类型：新增
     */
    Integer PROGRAM_CHANGE_TYPE_ADD = 1;

    /**
     * 应用系统变更类型：更新
     */
    Integer PROGRAM_CHANGE_TYPE_UPDATE = 2;

    /**
     * 应用系统变更类型：删除
     */
    Integer PROGRAM_CHANGE_TYPE_DELETE = 3;

    /**
     * 系统变更初始值
     */
    Integer PROGRAM_INIT_VERSION = 1;

    /**
     * 变更数据未推送（处置）
     */
    Integer PROGRAM_NOT_PROCESSED = 0;

    /**
     * 变更数据已推送（处置）
     */
    Integer PROGRAM_PROCESSED = 1;
}
