package cn.harmonycloud.development.convert.thgn;

import cn.harmonycloud.development.pojo.dto.thgn.ApplicationUserDTO;
import cn.harmonycloud.development.pojo.entity.thgn.ApplicationProgram;
import cn.harmonycloud.development.pojo.vo.thgn.app.ApplicationProgramVO;
import cn.harmonycloud.trinasolar.model.UserVO;
import org.mapstruct.Mapper;


import java.util.List;

@Mapper(componentModel = "spring") // 包含日期转换
public interface ApplicationProgramConvert {

    ApplicationProgramVO toVO(ApplicationProgram entity);

    List<ApplicationProgramVO> toVOList(List<ApplicationProgram> entities);

    List<ApplicationUserDTO> toApplicationUserDTOs(List<UserVO> users);
}