package cn.harmonycloud.development.pojo.entity.thgn;


import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 应用程序表
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@TableName(value ="application_component")
@Data
public class ApplicationComponent extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long applicationId;

    private String applicationType;

    private String component;

    private String componentKey;

}
