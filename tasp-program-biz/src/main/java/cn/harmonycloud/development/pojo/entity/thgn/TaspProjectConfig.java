package cn.harmonycloud.development.pojo.entity.thgn;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("tasp_project_config")
public class TaspProjectConfig {

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 应用系统id
     */
    private Long projectId;

    /**
     * 配置Key
     */
    private String configKey;


    /**
     * 配置内容
     */
    private String configContent;

    /**
     * 配置名称
     */
    private String configName;

}
