package cn.harmonycloud.development.pojo.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CPU指标数据VO
 */
@Data
@ApiModel(description = "CPU指标数据")
public class CpuMetricsVO {

    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    @ApiModelProperty(value = "实例名称")
    private String podName;

    @ApiModelProperty(value = "CPU使用率数据点列表")
    private List<CpuDataPoint> dataPoints;

    @ApiModelProperty(value = "时间范围开始时间")
    private Long startTime;

    @ApiModelProperty(value = "时间范围结束时间")
    private Long endTime;

    /**
     * CPU数据点
     */
    @Data
    @ApiModel(description = "CPU数据点")
    public static class CpuDataPoint {

        @ApiModelProperty(value = "时间戳，精确到秒")
        private Long timestamp;

        @ApiModelProperty(value = "CPU使用率，单位百分比")
        private Double cpuUsagePercentage;

        @ApiModelProperty(value = "时间格式化字符串，格式为yyyy/MM/dd HH:mm:ss")
        private String timeString;
    }
}
