package cn.harmonycloud.development.pojo.vo.thgn.app;


import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.List;

/**
 * @Description 应用程序表
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@Data
public class ApplicationProgramVO extends BaseEntity {

    private Long id;

    /**
     * 应用程序的唯一标识
     */
    private String applicationId;

    /**
     * 应用系统id
     */
    private String systemId;

    /**
     * 应用程序代码
     */
    private String programCode;

    /**
     * 应用程序中文名称
     */
    private String programNameCn;

    /**
     * 应用程序英文名称
     */
    private String programNameEn;


    /**
     * 初始化状态（0:等待初始化 1:已初始化）
     */
    private String initStatus; // 0:等待初始化 1:已初始化

    /**
     * 应用程序当前运行的版本号
     */
    private String currentRunningVersion;

    /**
     * 应用程序状态（0:未开始 1:开发阶段 2:测试阶段 3:发布阶段 4:生产阶段 5:已废弃）
     */
    private String programStatus;

    /**
     * 应用程序的技术特征标签
     */
    private String technicalStackTags;


    /**
     * 应用程序的技术特征标签
     */
    private String technologyStack;


    /**
     * 应用程序的健康程度指标
     */
    private String health;


    /**
     * 技术负责人
     */
    private List<Long> developDirectorId;


    /**
     * 应用程序描述
     */
    private String programDescCn;


    /**
     * gitlab代码仓库
     */
    private String gitlabRepoUrl;


    /**
     * 脚手架模版id
     */
    private String scaffoldTemplateId;


    /**
     * 创建人中文名称
     */
    private String createdBy;



    private String gitlabId;


    /**
     * 实例数
     */
    private Long podCounts;

}
