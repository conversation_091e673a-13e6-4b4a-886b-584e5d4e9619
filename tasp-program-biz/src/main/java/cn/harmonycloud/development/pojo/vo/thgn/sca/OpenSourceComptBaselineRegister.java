package cn.harmonycloud.development.pojo.vo.thgn.sca;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 开源组件基线实体类
 *
 * <AUTHOR> @date
 */
@Schema(description = "开源组件基登记")
@Data
@TableName("register_open_source_component") // 添加数据库表映射
public class OpenSourceComptBaselineRegister {

    /**
     * 唯一标识（主键）
     */
    @Schema(description = "唯一标识（主键）", example = "1")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称", required = true, example = "Spring Boot")
    private String name;

    /**
     * 分类
     */
    @Schema(description = "分类", required = true, example = "框架")
    private String category;

    /**
     * 版本
     */
    @Schema(description = "版本", required = true, example = "2.7.0")
    private String version;


    /**
     * 版本
     */
    @Schema(description = "许可凭证", required = true, example = "xss")
    private String permit;


    /**
     * 版本
     */
    @Schema(description = "最低版本", required = true, example = "1.7.0")
    private String minVersion;


    /**
     * 负责人
     */
    @Schema(description = "负责人", example = "张三")
    private String manager;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "企业级应用开发框架")
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2023-01-01T12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID", example = "1001")
    private Long createBy;

    /**
     * 更新人
     */
    @Schema(description = "更新人ID", example = "1001")
    private Long updateBy;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识（0-未删除，1-已删除）", example = "0")
    @TableLogic
    private Integer delFlag;

}
