package cn.harmonycloud.development.pojo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 扫描状态枚举类
 *
 * <AUTHOR>
@Getter
public enum ScanStatusEnum {

    /**
     * 扫描中
     */
    SCANNING(1, "扫描中"),

    /**
     * 通过
     */
    SCAN_PASSED(2, "扫描完成"),

    /**
     * 未通过
     */
    SCAN_FAILED(3, "未通过");

    @EnumValue
    private final int code;

    @JsonValue
    private final String description;

    ScanStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据描述获取枚举
     *
     * @param description 描述
     * @return 枚举
     */
    public static ScanStatusEnum getByDescription(String description) {
        for (ScanStatusEnum status : values()) {
            if (status.description.equals(description)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static ScanStatusEnum getByCode(int code) {
        for (ScanStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}