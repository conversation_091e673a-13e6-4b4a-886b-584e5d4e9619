package cn.harmonycloud.development.pojo.vo.thgn.sca;

import cn.harmonycloud.development.pojo.enums.ScanStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 扫描记录响应VO
 *
 * <AUTHOR> @date
 */
@ApiModel(value = "扫描记录响应VO")
@Data
public class ScanRecordResponse {

    /**
     * 唯一标识（主键）
     */
    @ApiModelProperty(value = "唯一标识（主键）", example = "1")
    private Long id;

    /**
     * 应用程序id
     */
    @ApiModelProperty(value = "应用程序id", required = true, example = "1001")
    private Long appId;

    /**
     * 扫描编号
     */
    @ApiModelProperty(value = "扫描编号", required = true, example = "#4020334591239453")
    private String scanId;

    /**
     * 提交id
     */
    @ApiModelProperty(value = "提交id", example = "a14392d2w32359ow3282492dfsd32341314f1")
    private String commitId;

    /**
     * 分支
     */
    @ApiModelProperty(value = "分支", example = "master")
    private String branch;

    /**
     * 触发方式
     */
    @ApiModelProperty(value = "触发方式", example = "手动触发")
    private String triggerMethod;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 耗时(秒)
     */
    @ApiModelProperty(value = "耗时(秒)", example = "70")
    private Integer duration;

    /**
     * 扫描状态
     */
    @ApiModelProperty(value = "扫描状态", example = "PASSED")
    private ScanStatusEnum status;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名", example = "张三")
    private String createByName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名", example = "李四")
    private String updateByName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}