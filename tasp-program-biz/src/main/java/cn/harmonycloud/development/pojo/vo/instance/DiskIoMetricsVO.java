package cn.harmonycloud.development.pojo.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 磁盘IO指标数据VO
 */
@Data
@ApiModel(description = "磁盘IO指标数据")
public class DiskIoMetricsVO {

    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    @ApiModelProperty(value = "磁盘读取数据点列表")
    private List<DiskIoDataPoint> dataPoints;

    @ApiModelProperty(value = "时间范围开始时间")
    private Long startTime;

    @ApiModelProperty(value = "时间范围结束时间")
    private Long endTime;

    /**
     * 磁盘IO数据点
     */
    @Data
    @ApiModel(description = "磁盘IO数据点")
    public static class DiskIoDataPoint {

        @ApiModelProperty(value = "时间戳，精确到秒")
        private Long timestamp;

        @ApiModelProperty(value = "磁盘读取速度，单位MB/s")
        private Double readSpeedMBps;

        @ApiModelProperty(value = "磁盘写入速度，单位MB/s")
        private Double writeSpeedMBps;

        @ApiModelProperty(value = "时间格式化字符串，格式为yyyy/MM/dd HH:mm:ss")
        private String timeString;
    }
}
