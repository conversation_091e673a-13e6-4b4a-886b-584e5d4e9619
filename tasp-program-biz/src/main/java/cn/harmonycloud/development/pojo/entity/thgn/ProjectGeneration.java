package cn.harmonycloud.development.pojo.entity.thgn;


import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 项目生成记录表
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@TableName(value ="project_generation")
@Data
public class ProjectGeneration extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String description;

    private String category;


    private String gitlabProjectId;

    private String gitRepoUrl;

    private String defaultBranch;

    private String version;

    private String techStack;



}
