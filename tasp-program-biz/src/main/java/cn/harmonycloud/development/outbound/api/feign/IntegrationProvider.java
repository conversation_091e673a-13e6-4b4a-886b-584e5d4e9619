package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.issue.model.CommonResult;
import cn.harmonycloud.trinasolar.model.AppCreateReqDTO;
import cn.harmonycloud.trinasolar.model.PageResult;
import cn.harmonycloud.trinasolar.model.vo.*;
import cn.hutool.json.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "kepler-integration", path = "/kepler/integration")
 public interface IntegrationProvider {
    @PostMapping({"/integration/app/create"})
    CommonResult<Boolean> createApp(@Valid @RequestBody AppCreateReqDTO appCreateReqDTO);

    @GetMapping("/devops/pipelines/deploy")
    CommonResult<JSONObject> deploy(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "pipelineId") String pipelineId);

    @GetMapping("/devops/pipelines/grant")
    CommonResult<Boolean> grant(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "appName") String appName, @RequestParam(value = "userCodes") List<String> userCodes);

    @DeleteMapping("/devops/pipelines/revoke")
    CommonResult<Boolean> revoke(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "appName") String appName, @RequestParam(value = "userCodes") List<String> userCodes);

    /**
     * 创建Devops产品
     *
     * @param appSystemVO
     * @return
     */
    @PostMapping("/devops/create/product")
    CommonResult<DevOpsProjectRespVO> createProject(@Valid @RequestBody AppSystemVO appSystemVO);

    /**
     * 获取流水线列表
     *
     * @param programId
     * @return
     */
    @GetMapping("/devops/pipelines")
    CommonResult<PageResult<PipelineRespVO>> getPipelines(@RequestParam(value = "programId") Long programId);

    /**
     * 删除应用下的流水线
     *
     * @param programId
     * @return
     */
    @DeleteMapping("/devops/pipelines/{programId}")
    CommonResult<Boolean> batchDeleteByProgramId(@PathVariable Long programId);


    /**
     * 获取流水线构建执行记录
     *
     * @param projectCode
     * @param pipelineId
     * @return
     */
    @GetMapping("/devops/pipelines/history")
    CommonResult<PageResult<DevOpsPipelineHisRespVO>> getPipelinesHistory(
            @RequestParam("projectCode") String projectCode, @RequestParam("pipelineId") String pipelineId);

    /**
     * 获取流水线执行记录日志
     *
     * @param projectCode
     * @param pipelineId
     * @param buildId
     * @return
     */
    @GetMapping("/devops/pipelines/logs")
    CommonResult<DevOpsPipelineBuildLogRespVO> getPipelinesLogs(@RequestParam("projectCode") String projectCode,
                                                                @RequestParam("pipelineId") String pipelineId,
                                                                @RequestParam("buildId") String buildId);


    /**
     * 查询应用系统是否初始化完成
     *
     * @param appSystemId
     * @return
     */
    @GetMapping("/project/config/initCheck")
    CommonResult<Boolean> initCheck(@RequestParam("appSystemId") Long appSystemId);
}
