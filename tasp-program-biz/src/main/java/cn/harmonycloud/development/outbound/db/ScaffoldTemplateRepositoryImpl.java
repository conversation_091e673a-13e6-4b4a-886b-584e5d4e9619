package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ScaffoldTemplateMapper;
import cn.harmonycloud.development.outbound.thgn.ScaffoldTemplateRepository;
import cn.harmonycloud.development.pojo.entity.thgn.ScaffoldTemplate;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;


/**
 * @Description
 * <AUTHOR> @Date 2025/03/25
 **/
@Service
public class ScaffoldTemplateRepositoryImpl extends BaseRepositoryImpl<ScaffoldTemplateMapper, ScaffoldTemplate> implements ScaffoldTemplateRepository {





}
