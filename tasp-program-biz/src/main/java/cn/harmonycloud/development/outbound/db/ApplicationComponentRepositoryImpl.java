package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ApplicationComponentMapper;
import cn.harmonycloud.development.outbound.thgn.ApplicationComponentRepository;
import cn.harmonycloud.development.pojo.entity.thgn.ApplicationComponent;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;


/**
 * @Description
 * <AUTHOR> @Date 2025/03/25
 **/
@Service
public class ApplicationComponentRepositoryImpl extends BaseRepositoryImpl<ApplicationComponentMapper, ApplicationComponent> implements ApplicationComponentRepository {





}
