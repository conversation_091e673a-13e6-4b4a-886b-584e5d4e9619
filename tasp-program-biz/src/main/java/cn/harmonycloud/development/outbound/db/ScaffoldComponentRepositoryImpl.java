package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ScaffoldComponentMapper;
import cn.harmonycloud.development.outbound.thgn.ScaffoldComponentRepository;
import cn.harmonycloud.development.pojo.entity.thgn.ScaffoldComponent;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;

@Service
public class ScaffoldComponentRepositoryImpl extends BaseRepositoryImpl<ScaffoldComponentMapper, ScaffoldComponent> implements ScaffoldComponentRepository {
}
