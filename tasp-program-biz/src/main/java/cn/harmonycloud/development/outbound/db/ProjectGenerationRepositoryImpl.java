package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ProjectGenerationMapper;
import cn.harmonycloud.development.outbound.thgn.ProjectGenerationRepository;
import cn.harmonycloud.development.pojo.entity.thgn.ProjectGeneration;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;


/**
 * @Description
 * <AUTHOR> @Date 2025/03/25
 **/
@Service
public class ProjectGenerationRepositoryImpl extends BaseRepositoryImpl<ProjectGenerationMapper, ProjectGeneration> implements ProjectGenerationRepository {





}
