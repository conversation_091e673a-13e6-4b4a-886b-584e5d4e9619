package cn.harmonycloud.development.outbound.trinasolar;

import cn.harmonycloud.development.pojo.entity.thgn.DevopsApplicationProgramPersonnel;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */


public interface ApplicationPersonnelRepository extends IService<DevopsApplicationProgramPersonnel> {
    // 根据应用程序ID查询成员列表
    default List<DevopsApplicationProgramPersonnel> listByProgramId(Long programId) {
        return this.lambdaQuery()
                .eq(DevopsApplicationProgramPersonnel::getProgramId, programId)
                .eq(DevopsApplicationProgramPersonnel::getDelFlag, "0")
                .list();
    }
}