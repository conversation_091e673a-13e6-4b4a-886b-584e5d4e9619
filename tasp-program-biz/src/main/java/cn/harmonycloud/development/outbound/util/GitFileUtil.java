package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.pojo.entity.thgn.ScaffoldComponent;
import cn.harmonycloud.enums.ExceptionCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class GitFileUtil {

    /**
     * 处理后端代码，替换包名和项目名称
     *
     * @param sourcePath
     * @param targetPath
     * @param programNameEn
     * @param targetPackage
     * @param serviceName
     * @param packagePath
     * @throws IOException
     */
    public static void replaceAppNameAndPackage(Path sourcePath, Path targetPath, String programNameEn,
                                                String targetPackage, String serviceName, String packagePath) throws IOException {
        // 源包名和目标包名
        String sourcePackage = packagePath;

        // 源包路径和目标包路径（将点号替换为路径分隔符）
        String sourcePackagePath = sourcePackage.replace('.', File.separatorChar);
        String targetPackagePath = targetPackage.replace('.', File.separatorChar);

        log.info("开始替换项目名和包名:");
        log.info("  源包名: {} -> 目标包名: {}", sourcePackage, targetPackage);
        log.info("  源包路径: {} -> 目标包路径: {}", sourcePackagePath, targetPackagePath);
        log.info("  脚手架名: {} -> 项目名: {}", serviceName, programNameEn);

        // 需要处理的后端配置文件
        List<String> backendConfigFiles = Arrays.asList(
                "pom.xml", "application.yml", "application-dev.yml", ".gitignore"
        );

        // 需要处理的Java文件扩展名
        List<String> javaExtensions = Arrays.asList(".java");

        // 用于存储需要重命名的目录映射
        Map<Path, Path> directoriesToRename = new HashMap<>();

        // 遍历源目录中的所有文件
        Files.walk(sourcePath)
                .filter(path -> !isGitDirectory(path))
                .forEach(source -> {
                    try {
                        // 获取相对路径
                        Path relativePath = sourcePath.relativize(source);
                        String pathStr = relativePath.toString();

                        // 替换项目名称
                        if (pathStr.contains(serviceName)) {
                            String oldPath = pathStr;
                            pathStr = pathStr.replace(serviceName, programNameEn);
                            if (!oldPath.equals(pathStr)) {
                                log.info("路径中替换项目名: {} -> {}", oldPath, pathStr);
                            }
                        }

                        // 替换包路径
                        if (pathStr.contains(sourcePackagePath)) {
                            String oldPath = pathStr;
                            pathStr = pathStr.replace(sourcePackagePath, targetPackagePath);
                            if (!oldPath.equals(pathStr)) {
                                log.info("路径中替换包名: {} -> {}", oldPath, pathStr);
                            }
                        }

                        // 创建新的相对路径
                        Path newRelativePath = Paths.get(pathStr);
                        Path dest = targetPath.resolve(newRelativePath);

                        if (Files.isDirectory(source)) {
                            // 创建目标目录
                            if (!Files.exists(dest)) {
                                Files.createDirectories(dest);
                            }

                            // 如果目录名包含项目名称，记录下来以便后续重命名
                            if (source.getFileName().toString().equals(serviceName)) {
                                Path newDest = dest.getParent().resolve(programNameEn);
                                directoriesToRename.put(dest, newDest);
                            }
                        } else {
                            // 确保父目录存在
                            Files.createDirectories(dest.getParent());

                            // 获取文件名和扩展名
                            String fileName = source.getFileName().toString();
                            String fileExtension = getFileExtension(fileName);

                            // 判断是否需要处理内容
                            boolean isConfigFile = backendConfigFiles.contains(fileName);
                            boolean isJavaFile = javaExtensions.contains(fileExtension);

                            if (isConfigFile || isJavaFile) {
                                // 读取文件内容
                                String content = new String(Files.readAllBytes(source), StandardCharsets.UTF_8);
                                String originalContent = content;
                                boolean contentChanged = false;

                                log.debug("处理文件: {}", fileName);
                                log.debug("文件类型: {} (配置文件: {}, Java文件: {})", fileExtension, isConfigFile, isJavaFile);

                                // 替换包名 - 使用更精确的替换策略
                                if (!sourcePackage.equals(targetPackage)) {
                                    String newContent = replacePackageName(content, sourcePackage, targetPackage);
                                    if (!newContent.equals(content)) {
                                        content = newContent;
                                        contentChanged = true;
                                        log.info("文件 {} 中替换包名: {} -> {}", fileName, sourcePackage, targetPackage);
                                    }
                                }

                                // 替换项目名称 - 使用更精确的替换策略
                                if (!serviceName.equals(programNameEn)) {
                                    String newContent = replaceProjectName(content, serviceName, programNameEn);
                                    if (!newContent.equals(content)) {
                                        content = newContent;
                                        contentChanged = true;
                                        log.info("文件 {} 中替换项目名: {} -> {}", fileName, serviceName, programNameEn);
                                    }
                                }

                                if (contentChanged) {
                                    // 写入修改后的内容
                                    Files.write(dest, content.getBytes(StandardCharsets.UTF_8));
                                    log.info("已更新文件: {}", newRelativePath);
                                } else {
                                    // 无需替换，直接复制
                                    Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                                    log.debug("文件无需替换，直接复制: {}", fileName);
                                }
                            } else {
                                // 非需要处理的文件，直接复制
                                Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                            }
                        }
                    } catch (IOException e) {
                        log.error("处理后端代码失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "处理后端代码失败: " + e.getMessage());
                    }
                });

        // 重命名收集到的目录
        renameDirectories(directoriesToRename);

        // 重命名包路径目录
        renamePackageDirectories(targetPath, sourcePackagePath, targetPackagePath);

        log.info("项目名和包名替换完成");
    }

    /**
     * 更精确地替换包名
     * 避免误替换其他包含相同字符串的内容
     */
    private static String replacePackageName(String content, String sourcePackage, String targetPackage) {
        // Java文件中的包声明
        content = content.replaceAll("package\\s+" + Pattern.quote(sourcePackage) + "\\s*;",
                                   "package " + targetPackage + ";");

        // Java文件中的import语句
        content = content.replaceAll("import\\s+" + Pattern.quote(sourcePackage),
                                   "import " + targetPackage);

        // XML文件中的包名引用
        content = content.replace(sourcePackage, targetPackage);

        return content;
    }

    /**
     * 更精确地替换项目名称
     * 针对不同文件类型使用不同的替换策略
     */
    private static String replaceProjectName(String content, String scaffoldName, String programNameEn) {
        // XML文件中的artifactId和name标签
        content = content.replaceAll("<artifactId>" + Pattern.quote(scaffoldName) + "</artifactId>",
                                   "<artifactId>" + programNameEn + "</artifactId>");
        content = content.replaceAll("<name>" + Pattern.quote(scaffoldName) + "</name>",
                                   "<name>" + programNameEn + "</name>");

        // YAML文件中的应用名称
        content = content.replaceAll("name:\\s*" + Pattern.quote(scaffoldName),
                                   "name: " + programNameEn);

        // Java文件中的类名和注释
        content = content.replace(scaffoldName, programNameEn);

        return content;
    }

    /**
     * 重命名包路径目录
     */
    private static void renamePackageDirectories(Path targetPath, String sourcePackagePath, String targetPackagePath) {
        if (sourcePackagePath.equals(targetPackagePath)) {
            return;
        }

        try {
            // 查找src/main/java目录
            Path javaSourcePath = targetPath.resolve("src/main/java");
            if (!Files.exists(javaSourcePath)) {
                log.warn("未找到Java源码目录: {}", javaSourcePath);
                return;
            }

            Path sourcePackageDir = javaSourcePath.resolve(sourcePackagePath);
            Path targetPackageDir = javaSourcePath.resolve(targetPackagePath);

            if (Files.exists(sourcePackageDir)) {
                // 确保目标包的父目录存在
                Files.createDirectories(targetPackageDir.getParent());

                // 移动整个包目录
                Files.move(sourcePackageDir, targetPackageDir);
                log.info("已重命名包目录: {} -> {}", sourcePackageDir, targetPackageDir);

                // 清理空的源包父目录
                cleanupEmptyDirectories(javaSourcePath, sourcePackagePath);
            } else {
                log.warn("源包目录不存在: {}", sourcePackageDir);
            }

        } catch (IOException e) {
            log.error("重命名包目录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理空的目录
     */
    private static void cleanupEmptyDirectories(Path basePath, String packagePath) {
        String[] pathParts = packagePath.split(Pattern.quote(File.separator));
        Path currentPath = basePath;

        // 从最深层开始检查并删除空目录
        for (int i = 0; i < pathParts.length - 1; i++) {
            currentPath = currentPath.resolve(pathParts[i]);
            try {
                if (Files.exists(currentPath) && isDirectoryEmpty(currentPath)) {
                    Files.delete(currentPath);
                    log.debug("删除空目录: {}", currentPath);
                }
            } catch (IOException e) {
                log.debug("删除空目录失败: {}", currentPath);
                break;
            }
        }
    }

    /**
     * 检查目录是否为空
     */
    private static boolean isDirectoryEmpty(Path directory) throws IOException {
        try (var stream = Files.list(directory)) {
            return !stream.findFirst().isPresent();
        }
    }

    /**
     * 重命名收集到的目录
     */
    public static void renameDirectories(Map<Path, Path> directoriesToRename) {
        // 从深层次目录开始，避免父目录重命名后找不到子目录
        directoriesToRename.entrySet().stream()
                .sorted((e1, e2) -> e2.getKey().toString().length() - e1.getKey().toString().length())
                .forEach(entry -> {
                    try {
                        Path oldPath = entry.getKey();
                        Path newPath = entry.getValue();

                        if (Files.exists(oldPath) && !Files.exists(newPath)) {
                            Files.move(oldPath, newPath);
                            log.info("重命名目录: {} -> {}", oldPath, newPath);
                        }
                    } catch (IOException e) {
                        log.error("重命名目录失败: {}", e.getMessage());
                        throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "重命名目录失败: " + e.getMessage());
                    }
                });
    }


    /**
     * 获取文件扩展名（包含点号）
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }

    /**
     * 判断路径是否是 Git 目录或其子目录
     * 只过滤 .git 目录本身，保留 .gitignore 等文件
     */
    public static boolean isGitDirectory(Path path) {
        Path fileName = path.getFileName();
        if (fileName == null) {
            return false;
        }

        // 检查当前路径是否是 .git 目录
        if (".git".equals(fileName.toString())) {
            log.debug("过滤 .git 目录: {}", path);
            return true;
        }

        // 检查路径中是否包含 .git 目录（但不是 .gitignore 等文件）
        Path current = path;
        while (current != null) {
            Path currentFileName = current.getFileName();
            if (currentFileName != null && ".git".equals(currentFileName.toString())) {
                log.debug("过滤 .git 子目录: {}", path);
                return true;
            }
            current = current.getParent();
        }

        // 特别记录 .gitignore 文件的处理
        if (".gitignore".equals(fileName.toString())) {
            log.info("保留 .gitignore 文件: {}", path);
        }

        return false;
    }

    /**
     * 给pom 添加dependence依赖
     *
     * @param targetPath
     * @param scaffoldComponents
     */
    /**
     * 添加POM依赖组件到pom.xml文件
     * 在现有dependencies基础上新增dependency依赖项，支持智能格式化
     *
     * 功能说明：
     * 1. 读取现有的pom.xml文件，保持原有依赖不变
     * 2. 自动检测现有POM文件的缩进样式（空格或制表符）
     * 3. 在</dependencies>标签前插入新的dependency依赖
     * 4. 自动格式化新增依赖，保持与现有代码风格一致
     * 5. 支持完整dependency块和简化格式的自动转换
     * 6. 过滤空值和无效依赖
     *
     * 支持的输入格式：
     * - 完整格式：<dependency><groupId>...</groupId>...</dependency>
     * - 简化格式：<groupId>...</groupId><artifactId>...</artifactId>...
     *
     * 格式化特性：
     * - 自动检测并匹配现有文件的缩进风格
     * - 智能处理XML标签的层级缩进
     * - 确保输出格式符合Maven标准
     *
     * @param targetPath         目标路径
     * @param scaffoldComponents 要添加的组件依赖列表
     * @throws GitException 当pom.xml格式错误或文件操作失败时抛出
     */
    public static void addPomStarterComponent(Path targetPath, List<ScaffoldComponent> scaffoldComponents) {
        List<String> depContentList = scaffoldComponents.stream().map(ScaffoldComponent::getDepContent).collect(Collectors.toList());

        File pomFile = targetPath.resolve("pom.xml").toFile();
        try {
            String pomContent = new String(Files.readAllBytes(pomFile.toPath()), StandardCharsets.UTF_8);

            // 查找dependencies结束标签的位置
            String dependenciesEnd = "</dependencies>";
            int endIndex = pomContent.indexOf(dependenciesEnd);

            if (endIndex == -1) {
                log.error("pom.xml中未找到</dependencies>标签");
                throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "pom.xml格式错误：缺少</dependencies>标签");
            }

            // 检测现有依赖的缩进样式
            String indentStyle = detectIndentStyle(pomContent, endIndex);

            // 构建新的依赖内容
            StringBuilder newDependencies = new StringBuilder();
            for (String component : depContentList) {
                if (component != null && !component.trim().isEmpty()) {
                    String formattedDependency = formatMavenDependency(component.trim(), indentStyle);
                    newDependencies.append(formattedDependency);
                }
            }

            // 在</dependencies>标签前插入新的依赖
            String updatedPomContent = pomContent.substring(0, endIndex) +
                    newDependencies.toString() +
                    pomContent.substring(endIndex);

            // 将修改后的pom.xml写入到targetPath目录
            Path targetPomPath = targetPath.resolve("pom.xml");
            Files.write(targetPomPath, updatedPomContent.getBytes(StandardCharsets.UTF_8));

            log.info("成功添加 {} 个组件依赖到 pom.xml，并保存到: {}", scaffoldComponents.size(), targetPomPath);

        } catch (IOException e) {
            log.error("修改pom文件失败: {}", e.getMessage());
            throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "修改pom文件失败: " + e.getMessage());
        }
    }

    /**
     * 检测现有POM文件的缩进样式
     *
     * @param pomContent POM文件内容
     * @param dependenciesEndIndex dependencies结束标签的位置
     * @return 缩进字符串（空格或制表符）
     */
    private static String detectIndentStyle(String pomContent, int dependenciesEndIndex) {
        // 从dependencies结束标签向前查找，寻找现有dependency的缩进
        String[] lines = pomContent.substring(0, dependenciesEndIndex).split("\n");

        // 从后往前查找最近的dependency标签
        for (int i = lines.length - 1; i >= 0; i--) {
            String line = lines[i];
            if (line.contains("<dependency>") || line.contains("<groupId>") || line.contains("<artifactId>")) {
                // 计算该行的缩进
                int indentCount = 0;
                for (char c : line.toCharArray()) {
                    if (c == ' ') {
                        indentCount++;
                    } else if (c == '\t') {
                        return "\t"; // 如果使用制表符，直接返回
                    } else {
                        break;
                    }
                }

                // 根据标签类型确定基础缩进
                if (line.contains("<dependency>")) {
                    return " ".repeat(Math.max(4, indentCount)); // dependency标签的缩进
                } else if (line.contains("<groupId>") || line.contains("<artifactId>")) {
                    // groupId/artifactId通常比dependency多缩进4个空格
                    int baseIndent = Math.max(4, indentCount - 4);
                    return " ".repeat(baseIndent);
                }
            }
        }

        // 如果没有找到现有依赖，使用默认的4个空格缩进
        return "    ";
    }

    /**
     * 格式化Maven依赖为标准格式
     *
     * @param dependency 原始依赖内容
     * @param baseIndent 基础缩进字符串
     * @return 格式化后的依赖字符串
     */
    private static String formatMavenDependency(String dependency, String baseIndent) {
        // 如果已经是完整的dependency块，重新格式化
        if (dependency.contains("<dependency>")) {
            return reformatExistingDependency(dependency, baseIndent);
        }

        // 如果是简化格式，构建完整的dependency块
        return buildDependencyBlock(dependency, baseIndent);
    }

    /**
     * 重新格式化现有的dependency块
     */
    private static String reformatExistingDependency(String dependency, String baseIndent) {
        StringBuilder result = new StringBuilder();
        String[] lines = dependency.split("\n");

        result.append("\n");

        for (String line : lines) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) {
                continue;
            }

            if (trimmedLine.startsWith("<dependency>")) {
                result.append(baseIndent).append(trimmedLine).append("\n");
            } else if (trimmedLine.startsWith("</dependency>")) {
                result.append(baseIndent).append(trimmedLine).append("\n");
            } else if (trimmedLine.startsWith("<")) {
                // 其他XML标签，增加一级缩进
                result.append(baseIndent).append("    ").append(trimmedLine).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 从简化格式构建完整的dependency块
     */
    private static String buildDependencyBlock(String content, String baseIndent) {
        StringBuilder result = new StringBuilder();
        result.append("\n");
        result.append(baseIndent).append("<dependency>\n");

        String[] lines = content.split("\n");
        for (String line : lines) {
            String trimmedLine = line.trim();
            if (!trimmedLine.isEmpty() && trimmedLine.startsWith("<")) {
                result.append(baseIndent).append("    ").append(trimmedLine).append("\n");
            }
        }

        result.append(baseIndent).append("</dependency>\n");
        return result.toString();
    }

    /**
     * 添加应用配置到 application.yml 文件
     *
     * @param targetPath 目标路径
     * @param scaffoldComponents 脚手架组件列表，包含要合并的配置内容
     * @throws IOException 文件操作异常
     */
    public static void addApplicationConfig(Path targetPath, List<ScaffoldComponent> scaffoldComponents) throws IOException {
        ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());

        File applicationFile = targetPath.resolve("src/main/resources/application.yml").toFile();
        JsonNode applicationConfig = yamlMapper.readTree(applicationFile);

        // 提取要新增的配置
        List<Map<String, String>> configContentList = scaffoldComponents
                .stream()
                .map(ScaffoldComponent::getConfigContent)
                .filter(Objects::nonNull) // 过滤空配置
                .collect(Collectors.toList());

        log.info("开始合并 {} 个组件的配置到 application.yml", configContentList.size());

        for (Map<String, String> configContent : configContentList) {
            try {
                String contentStr = configContent.get("content");
                if (contentStr != null && !contentStr.trim().isEmpty()) {
                    JsonNode content = yamlMapper.readTree(contentStr);
                    if (content != null && !content.isNull()) {
                        applicationConfig = mergeYamlNodes(applicationConfig, content);
                        log.debug("成功合并配置: {}", contentStr);
                    }
                }
            } catch (JsonProcessingException e) {
                log.error("解析配置内容失败: {}", configContent.get("content"), e);
                throw new GitException(ExceptionCode.CREATE_MERGE_REQUEST_FAIL, "解析YAML配置失败: " + e.getMessage());
            }
        }

        // 确保目标目录存在
        Files.createDirectories(targetPath);

        // 将合并后的配置写回文件
        Path targetApplicationPath = targetPath.resolve("application.yml");
        yamlMapper.writeValue(targetApplicationPath.toFile(), applicationConfig);

        log.info("成功合并配置并保存到: {}", targetApplicationPath);
    }

    /**
     * 合并两个YAML节点
     *
     * 合并规则：
     * 1. 对象节点：递归合并，content中的值会覆盖applicationConfig中的同名字段
     * 2. 数组节点：content中的数组会替换applicationConfig中的数组
     * 3. 基本类型：content中的值会覆盖applicationConfig中的值
     * 4. null值：如果content为null，保持applicationConfig的值不变
     *
     * @param applicationConfig 原始配置节点
     * @param content 要合并的新配置节点
     * @return 合并后的配置节点
     */
    private static JsonNode mergeYamlNodes(JsonNode applicationConfig, JsonNode content) {
        if (content == null || content.isNull()) {
            return applicationConfig;
        }

        if (applicationConfig == null || applicationConfig.isNull()) {
            return content;
        }

        // 如果两个节点都是对象类型，进行深度合并
        if (applicationConfig.isObject() && content.isObject()) {
            return mergeObjectNodes((ObjectNode) applicationConfig, (ObjectNode) content);
        }

        // 如果两个节点都是数组类型，合并数组
        if (applicationConfig.isArray() && content.isArray()) {
            return mergeArrayNodes((ArrayNode) applicationConfig, (ArrayNode) content);
        }

        // 其他情况，content覆盖applicationConfig
        return content;
    }

    /**
     * 合并两个对象节点
     */
    private static ObjectNode mergeObjectNodes(ObjectNode target, ObjectNode source) {
        ObjectNode result = target.deepCopy();

        Iterator<Map.Entry<String, JsonNode>> fields = source.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            JsonNode sourceValue = entry.getValue();
            JsonNode targetValue = result.get(fieldName);

            if (targetValue != null && targetValue.isObject() && sourceValue.isObject()) {
                // 递归合并对象
                JsonNode mergedValue = mergeObjectNodes((ObjectNode) targetValue, (ObjectNode) sourceValue);
                result.set(fieldName, mergedValue);
            } else if (targetValue != null && targetValue.isArray() && sourceValue.isArray()) {
                // 合并数组
                JsonNode mergedValue = mergeArrayNodes((ArrayNode) targetValue, (ArrayNode) sourceValue);
                result.set(fieldName, mergedValue);
            } else {
                // 直接覆盖或添加新字段
                result.set(fieldName, sourceValue);
            }
        }

        return result;
    }

    /**
     * 合并两个数组节点
     * 策略：将source数组的元素添加到target数组中，避免重复
     */
    private static ArrayNode mergeArrayNodes(ArrayNode target, ArrayNode source) {
        ArrayNode result = target.deepCopy();

        for (JsonNode sourceElement : source) {
            // 检查是否已存在相同元素（避免重复）
            boolean exists = false;
            for (JsonNode targetElement : result) {
                if (targetElement.equals(sourceElement)) {
                    exists = true;
                    break;
                }
            }

            if (!exists) {
                result.add(sourceElement);
            }
        }

        return result;
    }

    /**
     * 测试YAML合并功能的示例方法
     *
     * 使用示例：
     * 原始配置：
     * server:
     *   port: 8080
     * spring:
     *   datasource:
     *     url: ********************************
     *
     * 新增配置：
     * server:
     *   servlet:
     *     context-path: /api
     * spring:
     *   datasource:
     *     username: root
     *     password: 123456
     *   redis:
     *     host: localhost
     *     port: 6379
     *
     * 合并结果：
     * server:
     *   port: 8080
     *   servlet:
     *     context-path: /api
     * spring:
     *   datasource:
     *     url: ********************************
     *     username: root
     *     password: 123456
     *   redis:
     *     host: localhost
     *     port: 6379
     */
//    public static void testYamlMerge() {
//        try {
//            ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());
//
//            String originalYaml = """
//                server:
//                  port: 8080
//                spring:
//                  datasource:
//                    url: ********************************
//                """;
//
//            String newYaml = """
//                server:
//                  servlet:
//                    context-path: /api
//                spring:
//                  datasource:
//                    username: root
//                    password: 123456
//                  redis:
//                    host: localhost
//                    port: 6379
//                """;
//
//            JsonNode original = yamlMapper.readTree(originalYaml);
//            JsonNode newConfig = yamlMapper.readTree(newYaml);
//            JsonNode merged = mergeYamlNodes(original, newConfig);
//
//            String result = yamlMapper.writeValueAsString(merged);
//            log.info("YAML合并测试结果:\n{}", result);
//
//        } catch (Exception e) {
//            log.error("YAML合并测试失败", e);
//        }
//    }

    /**
     * 测试包名和项目名替换功能
     */
//    public static void testReplaceAppNameAndPackage() {
//        try {
//            // 测试包名替换
//            String javaContent = """
//                package com.example.template;
//
//                import com.example.template.service.TestService;
//
//                public class TestClass {
//                    // template project
//                }
//                """;
//
//            String replacedContent = replacePackageName(javaContent, "com.example.template", "com.mycompany.myapp");
//            replacedContent = replaceProjectName(replacedContent, "template", "myapp");
//
//            log.info("包名和项目名替换测试结果:\n{}", replacedContent);
//
//            // 测试XML内容替换
//            String xmlContent = """
//                <project>
//                    <artifactId>template</artifactId>
//                    <name>template</name>
//                    <groupId>com.example.template</groupId>
//                </project>
//                """;
//
//            String replacedXml = replaceProjectName(xmlContent, "template", "myapp");
//            replacedXml = replacePackageName(replacedXml, "com.example.template", "com.mycompany.myapp");
//
//            log.info("XML替换测试结果:\n{}", replacedXml);
//
//        } catch (Exception e) {
//            log.error("替换功能测试失败", e);
//        }
//    }
}
