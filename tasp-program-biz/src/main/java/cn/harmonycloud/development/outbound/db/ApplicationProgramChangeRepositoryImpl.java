package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ApplicationProgramChangeMapper;
import cn.harmonycloud.development.outbound.trinasolar.ApplicationProgramChangeRepository;
import cn.harmonycloud.development.pojo.entity.thgn.ApplicationProgramChange;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @className: ApplicationProgramChangeRepositoryImpl
 * @Description: TODO
 * @author: pengshy
 * @date: 2025/9/2 15:45
 */
public class ApplicationProgramChangeRepositoryImpl extends ServiceImpl<ApplicationProgramChangeMapper, ApplicationProgramChange>
        implements ApplicationProgramChangeRepository {
}
