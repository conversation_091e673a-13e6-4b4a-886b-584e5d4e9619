package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.db.mapper.DevopsApplicationPipelineMapper;
import cn.harmonycloud.development.pojo.entity.thgn.DevopsApplicationPipeline;
import cn.harmonycloud.development.service.DevopsApplicationPipelineService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DevopsApplicationPipelineServiceImpl 
    extends ServiceImpl<DevopsApplicationPipelineMapper, DevopsApplicationPipeline>
    implements DevopsApplicationPipelineService {
}