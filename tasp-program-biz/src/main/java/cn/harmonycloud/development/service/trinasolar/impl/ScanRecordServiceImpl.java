package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ScanRecordMapper;
import cn.harmonycloud.development.pojo.entity.thgn.ScaScanRecord;
import cn.harmonycloud.development.pojo.enums.ScanStatusEnum;
import cn.harmonycloud.development.pojo.vo.thgn.sca.ScanRecordResponse;
import cn.harmonycloud.development.service.trinasolar.ScanRecordService;
import cn.harmonycloud.trinasolar.UpmsGatewayProvider;
import cn.harmonycloud.trinasolar.model.R;
import cn.harmonycloud.trinasolar.model.UserVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 扫描记录Service实现类
 *
 * <AUTHOR>
@Slf4j
@Service
public class ScanRecordServiceImpl extends ServiceImpl<ScanRecordMapper, ScaScanRecord> implements ScanRecordService {

    private static final String[] TRIGGER_METHODS = {"手动触发", "自动触发", "定时任务"};
    private static final ScanStatusEnum[] SCAN_STATUSES = {ScanStatusEnum.SCAN_PASSED, ScanStatusEnum.SCAN_FAILED, ScanStatusEnum.SCANNING};
    private static final Random RANDOM = new Random();

    @Autowired
    private UpmsGatewayProvider  upmsGatewayProvider;

    @Override
    public boolean batchInsertScanRecords(List<ScaScanRecord> scanRecords) {
        if (scanRecords == null || scanRecords.isEmpty()) {
            return true;
        }

        LocalDateTime now = LocalDateTime.now();
        Long userId = 1L; // 假设当前用户ID为1，实际应从上下文获取

        for (ScaScanRecord record : scanRecords) {
            record.setCreateBy(userId);
            record.setCreateTime(now);
            record.setUpdateBy(userId);
            record.setUpdateTime(now);
            record.setDelFlag(0);
        }

        try {
            return this.saveBatch(scanRecords);
        } catch (Exception e) {
            log.error("批量插入扫描记录失败", e);
            return false;
        }
    }

    @Override
    public Page<ScaScanRecord> getScanRecordsByAppId(Long appId, int pageNum, int pageSize) {
        if (appId == null) {
            return new Page<>();
        }
        Page<ScaScanRecord> page = new Page<>(pageNum, pageSize);
        return this.lambdaQuery()
                .eq(ScaScanRecord::getAppId, appId)
                .eq(ScaScanRecord::getDelFlag, 0).orderByDesc(ScaScanRecord::getUpdateTime)
                .page(page);
    }

    @Override
    public List<ScaScanRecord> mockScanData(Long appId) {
        if (appId == null) {
            throw new IllegalArgumentException("应用id不能为空");
        }

        List<ScaScanRecord> scanRecords = new ArrayList<>();
        int recordCount = 2 + RANDOM.nextInt(4); // 生成2-5条记录

        for (int i = 0; i < recordCount; i++) {
            ScaScanRecord record = new ScaScanRecord();
            record.setAppId(appId);
            record.setScanId("#" + UUID.randomUUID().toString().replace("-", "").substring(0, 14));
            record.setCommitId(UUID.randomUUID().toString().replace("-", ""));
            record.setBranch("master");
            record.setTriggerMethod(TRIGGER_METHODS[RANDOM.nextInt(TRIGGER_METHODS.length)]);
            record.setStartTime(LocalDateTime.now().minusMinutes(RANDOM.nextInt(120)));
            record.setDuration(30 + RANDOM.nextInt(120)); // 30-150秒
            record.setStatus(SCAN_STATUSES[RANDOM.nextInt(SCAN_STATUSES.length)]);
            record.setCreateBy(1L); // 模拟创建人ID
            record.setUpdateBy(1L); // 模拟更新人ID
            record.setCreateTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());

            scanRecords.add(record);
        }

        return scanRecords;
    }

    @Override
    public Page<ScanRecordResponse> getScanRecordResponsesByAppId(Long appId, int pageNum, int pageSize) {
        // 分页查询扫描记录
        Page<ScaScanRecord> scanRecordPage = getScanRecordsByAppId(appId, pageNum, pageSize);
        if (scanRecordPage.getRecords().isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 提取所有不重复的用户ID
        Set<Long> userIds = new HashSet<>();
        scanRecordPage.getRecords().forEach(record -> {
            if (record.getCreateBy() != null) {
                userIds.add(record.getCreateBy());
            }
            if (record.getUpdateBy() != null) {
                userIds.add(record.getUpdateBy());
            }
        });

        // 批量查询用户信息
        Map<Long, String> userIdToNameMap = new HashMap<>();
        try {
            R<List<UserVO>> result = upmsGatewayProvider.getByIds(new ArrayList<>(userIds));
            if (result.isSuccess() && result.getData() != null) {
                for (UserVO userVO : result.getData()) {
                    userIdToNameMap.put(userVO.getId(), userVO.getUserRealname());
                }
            }
        } catch (Exception e) {
            log.error("批量查询用户信息失败", e);
        }

        List<ScanRecordResponse> responses = new ArrayList<>();
        for (ScaScanRecord record : scanRecordPage.getRecords()) {
            ScanRecordResponse response = new ScanRecordResponse();
            // 复制基本属性
            response.setId(record.getId());
            response.setAppId(record.getAppId());
            response.setScanId(record.getScanId());
            response.setCommitId(record.getCommitId());
            response.setBranch(record.getBranch());
            response.setTriggerMethod(record.getTriggerMethod());
            response.setStartTime(record.getStartTime());
            response.setDuration(record.getDuration());
            response.setStatus(record.getStatus());
            response.setCreateTime(record.getCreateTime());
            response.setUpdateTime(record.getUpdateTime());

            // 设置创建人姓名
            response.setCreateByName(record.getCreateBy() != null ?
                    userIdToNameMap.getOrDefault(record.getCreateBy(), "未知") : "未知");

            // 设置更新人姓名
            response.setUpdateByName(record.getUpdateBy() != null ?
                    userIdToNameMap.getOrDefault(record.getUpdateBy(), "未知") : "未知");

            responses.add(response);
        }

        // 创建响应分页对象
        Page<ScanRecordResponse> responsePage = new Page<>(pageNum, pageSize);
        responsePage.setRecords(responses);
        responsePage.setTotal(scanRecordPage.getTotal());
        responsePage.setPages(scanRecordPage.getPages());

        return responsePage;
    }
}