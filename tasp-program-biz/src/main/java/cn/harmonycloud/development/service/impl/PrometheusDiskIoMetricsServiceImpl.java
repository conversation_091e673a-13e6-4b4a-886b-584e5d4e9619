package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.development.service.PrometheusDiskIoMetricsService;
import cn.harmonycloud.development.pojo.vo.instance.DiskIoMetricsVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Prometheus 磁盘IO指标服务实现类
 */
@Service
@Slf4j
public class PrometheusDiskIoMetricsServiceImpl implements PrometheusDiskIoMetricsService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    @Override
    public DiskIoMetricsVO getServiceDiskIoTrendFromPrometheus(String prometheusUrl, String namespace,
                                                               String serviceName, Integer timeRangeMinutes) {
        // 参数校验
        if (!StringUtils.hasText(prometheusUrl) || !StringUtils.hasText(namespace) ||
                !StringUtils.hasText(serviceName)) {
            throw new BusinessException("参数不能为空");
        }

        // 默认时间范围60分钟
        if (timeRangeMinutes == null || timeRangeMinutes <= 0) {
            timeRangeMinutes = 60;
        }

        try {
            // 创建返回对象
            DiskIoMetricsVO diskIoMetricsVO = new DiskIoMetricsVO();
            diskIoMetricsVO.setServiceName(serviceName);

            // 设置时间范围
            long endTime = Instant.now().getEpochSecond();
            long startTime = endTime - (timeRangeMinutes * 60);
            diskIoMetricsVO.setStartTime(startTime * 1000); // 转换为毫秒
            diskIoMetricsVO.setEndTime(endTime * 1000); // 转换为毫秒

            // 构建Prometheus查询语句 (磁盘读取速度)
            String readQuery = String.format(
                    "sum(rate(container_fs_reads_bytes_total{namespace=\"%s\", pod=~\"%s-.*\"}[5m])) by (pod)",
                    namespace, serviceName);

            // 构建Prometheus查询语句 (磁盘写入速度)
            String writeQuery = String.format(
                    "sum(rate(container_fs_writes_bytes_total{namespace=\"%s\", pod=~\"%s-.*\"}[5m])) by (pod)",
                    namespace, serviceName);

            // 获取数据点
            List<DiskIoMetricsVO.DiskIoDataPoint> dataPoints = queryPrometheusData(
                    prometheusUrl, readQuery, writeQuery, startTime, endTime);

            diskIoMetricsVO.setDataPoints(dataPoints);
            return diskIoMetricsVO;
        } catch (Exception e) {
            log.error("从Prometheus获取服务磁盘IO趋势失败，prometheusUrl={}, namespace={}, serviceName={}",
                    prometheusUrl, namespace, serviceName, e);
            throw new BusinessException("获取服务磁盘IO趋势失败", e);
        }
    }

    /**
     * 查询Prometheus数据
     *
     * @param prometheusUrl Prometheus服务器地址
     * @param readQuery     读取查询语句
     * @param writeQuery    写入查询语句
     * @param start         开始时间
     * @param end           结束时间
     * @return 磁盘IO数据点列表
     */
    private List<DiskIoMetricsVO.DiskIoDataPoint> queryPrometheusData(String prometheusUrl,
                                                                      String readQuery, String writeQuery,
                                                                      long start, long end) throws IOException {
        List<DiskIoMetricsVO.DiskIoDataPoint> dataPoints = new ArrayList<>();

        // 构建查询URL
        String encodedReadQuery = URLEncoder.encode(readQuery, StandardCharsets.UTF_8);
        String encodedWriteQuery = URLEncoder.encode(writeQuery, StandardCharsets.UTF_8);

        String readRangeQueryUrl = String.format("%s/api/v1/query_range?query=%s&start=%d&end=%d&step=30",
                prometheusUrl, encodedReadQuery, start, end);

        String writeRangeQueryUrl = String.format("%s/api/v1/query_range?query=%s&start=%d&end=%d&step=30",
                prometheusUrl, encodedWriteQuery, start, end);

        log.debug("Prometheus read query URL: {}", readRangeQueryUrl);
        log.debug("Prometheus write query URL: {}", writeRangeQueryUrl);

        // 获取读取数据
        List<DiskIoMetricsVO.DiskIoDataPoint> readDataPoints = queryPrometheusDataPoints(readRangeQueryUrl, true);

        // 获取写入数据
        List<DiskIoMetricsVO.DiskIoDataPoint> writeDataPoints = queryPrometheusDataPoints(writeRangeQueryUrl, false);

        // 合并数据点
        dataPoints = mergeDataPoints(readDataPoints, writeDataPoints);

        return dataPoints;
    }

    /**
     * 查询Prometheus数据点
     *
     * @param queryUrl 查询URL
     * @param isRead   是否为读取操作
     * @return 数据点列表
     */
    private List<DiskIoMetricsVO.DiskIoDataPoint> queryPrometheusDataPoints(String queryUrl, boolean isRead) throws IOException {
        List<DiskIoMetricsVO.DiskIoDataPoint> dataPoints = new ArrayList<>();

        // 发送HTTP请求
        URL url = new URL(queryUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(30000);

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 解析响应
            try (InputStream inputStream = connection.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                // 读取响应内容
                String response = reader.lines().collect(Collectors.joining("\n"));
                log.debug("Prometheus API response: {}", response);

                // 解析JSON响应
                dataPoints = parsePrometheusResponse(response, isRead);
            }
        } else {
            String errorMessage = String.format("Prometheus查询失败，响应码: %d", responseCode);
            log.error(errorMessage);
            throw new BusinessException(errorMessage);
        }

        return dataPoints;
    }

    /**
     * 解析Prometheus API响应
     *
     * @param response JSON格式的响应
     * @param isRead   是否为读取操作
     * @return 磁盘IO数据点列表
     */
    private List<DiskIoMetricsVO.DiskIoDataPoint> parsePrometheusResponse(String response, boolean isRead) {
        List<DiskIoMetricsVO.DiskIoDataPoint> dataPoints = new ArrayList<>();

        try {
            // 解析JSON响应
            JSONObject jsonResponse = JSON.parseObject(response);

            if ("success".equals(jsonResponse.getString("status"))) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data != null && "matrix".equals(data.getString("resultType"))) {
                    JSONArray results = data.getJSONArray("result");

                    if (results != null) {
                        for (int i = 0; i < results.size(); i++) {
                            JSONObject result = results.getJSONObject(i);
                            JSONArray values = result.getJSONArray("values");

                            if (values != null) {
                                for (int j = 0; j < values.size(); j++) {
                                    JSONArray valuePair = values.getJSONArray(j);

                                    if (valuePair != null && valuePair.size() == 2) {
                                        // 创建数据点
                                        DiskIoMetricsVO.DiskIoDataPoint dataPoint = new DiskIoMetricsVO.DiskIoDataPoint();

                                        // 获取时间戳（秒）并转换为毫秒
                                        long timestamp = valuePair.getLongValue(0) * 1000;
                                        dataPoint.setTimestamp(timestamp);

                                        // 获取磁盘IO速度（字节/秒）并转换为MB/s
                                        String valueStr = valuePair.getString(1);
                                        double ioSpeedInBytes = Double.parseDouble(valueStr);
                                        double ioSpeedInMBps = ioSpeedInBytes / (1024 * 1024);

                                        if (isRead) {
                                            dataPoint.setReadSpeedMBps(Math.round(ioSpeedInMBps * 100.0) / 100.0); // 保留两位小数
                                        } else {
                                            dataPoint.setWriteSpeedMBps(Math.round(ioSpeedInMBps * 100.0) / 100.0); // 保留两位小数
                                        }

                                        // 格式化时间字符串
                                        dataPoint.setTimeString(Instant.ofEpochMilli(timestamp)
                                                .atZone(ZoneId.systemDefault())
                                                .format(DATE_TIME_FORMATTER));

                                        dataPoints.add(dataPoint);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                String error = jsonResponse.getString("error");
                log.error("Prometheus API返回错误: {}", error);
                throw new BusinessException("Prometheus API返回错误: " + error);
            }
        } catch (Exception e) {
            log.error("解析Prometheus响应失败: {}", e.getMessage(), e);
            throw new BusinessException("解析Prometheus响应失败: " + e.getMessage());
        }

        return dataPoints;
    }

    /**
     * 合并读取和写入数据点
     *
     * @param readDataPoints  读取数据点
     * @param writeDataPoints 写入数据点
     * @return 合并后的数据点
     */
    private List<DiskIoMetricsVO.DiskIoDataPoint> mergeDataPoints(
            List<DiskIoMetricsVO.DiskIoDataPoint> readDataPoints,
            List<DiskIoMetricsVO.DiskIoDataPoint> writeDataPoints) {

        List<DiskIoMetricsVO.DiskIoDataPoint> mergedDataPoints = new ArrayList<>();

        // 以读取数据点为基础进行合并
        for (DiskIoMetricsVO.DiskIoDataPoint readPoint : readDataPoints) {
            DiskIoMetricsVO.DiskIoDataPoint mergedPoint = new DiskIoMetricsVO.DiskIoDataPoint();
            mergedPoint.setTimestamp(readPoint.getTimestamp());
            mergedPoint.setTimeString(readPoint.getTimeString());
            mergedPoint.setReadSpeedMBps(readPoint.getReadSpeedMBps());

            // 查找匹配的写入数据点
            for (DiskIoMetricsVO.DiskIoDataPoint writePoint : writeDataPoints) {
                if (writePoint.getTimestamp().equals(readPoint.getTimestamp())) {
                    mergedPoint.setWriteSpeedMBps(writePoint.getWriteSpeedMBps());
                    break;
                }
            }

            // 如果没有找到匹配的写入数据点，则设置为0
            if (mergedPoint.getWriteSpeedMBps() == null) {
                mergedPoint.setWriteSpeedMBps(0.0);
            }

            mergedDataPoints.add(mergedPoint);
        }

        return mergedDataPoints;
    }
}
