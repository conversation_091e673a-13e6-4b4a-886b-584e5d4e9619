package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.instance.DiskIoMetricsVO;

/**
 * Prometheus 磁盘IO指标服务接口
 */
public interface PrometheusDiskIoMetricsService {

    /**
     * 从Prometheus获取服务磁盘IO趋势
     *
     * @param prometheusUrl     Prometheus服务器地址
     * @param namespace         命名空间
     * @param serviceName       服务名称
     * @param timeRangeMinutes  时间范围（分钟）
     * @return 磁盘IO指标数据
     */
    DiskIoMetricsVO getServiceDiskIoTrendFromPrometheus(String prometheusUrl, String namespace,
                                                        String serviceName, Integer timeRangeMinutes);
}
