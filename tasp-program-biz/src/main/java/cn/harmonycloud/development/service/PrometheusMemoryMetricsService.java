package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.instance.MemoryMetricsVO;

/**
 * Prometheus内存指标服务接口
 */
public interface PrometheusMemoryMetricsService {

    /**
     * 从Prometheus获取服务内存使用趋势
     *
     * @param prometheusUrl     Prometheus服务器地址
     * @param namespace         命名空间
     * @param serviceName       服务名称
     * @param timeRangeMinutes  时间范围（分钟）
     * @return 内存指标数据
     */
    MemoryMetricsVO getServiceMemoryTrendFromPrometheus(String prometheusUrl, String namespace,
                                                        String serviceName, Integer timeRangeMinutes);
}
