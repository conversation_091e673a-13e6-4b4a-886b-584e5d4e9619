package cn.harmonycloud.development.service.mapstruct.thgn;

import cn.harmonycloud.development.pojo.dto.subsystem.*;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDTO;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.SubSystemConfig;
import cn.harmonycloud.development.pojo.entity.thgn.ApplicationProgram;
import cn.harmonycloud.development.pojo.vo.system.SubSystemDataVO;
import cn.harmonycloud.development.service.mapstruct.DevopsSubSystemMapstruct;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigDTO;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import cn.hutool.json.JSONUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/03/27
 */
@Mapper(componentModel = "spring")
public interface ApplicationProgramMapstruct {

    //@Mapping(target = "developDirectorId", expression = "java(toDevelopDirectorIdStr(request))")
    ApplicationProgram toApplicationProgram(ApplicationProgramDTO request);
//
//    default String toDevelopDirectorIdStr(ApplicationProgramDTO request){
//        return JSONUtil.toJsonStr(request.getDevelopDirectorId());
//    };

}
