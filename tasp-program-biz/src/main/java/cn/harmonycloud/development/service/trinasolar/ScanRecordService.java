package cn.harmonycloud.development.service.trinasolar;

import cn.harmonycloud.development.pojo.entity.thgn.ScaScanRecord;
import cn.harmonycloud.development.pojo.vo.thgn.sca.ScanRecordResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 扫描记录Service接口
 *
 * <AUTHOR>
public interface ScanRecordService extends IService<ScaScanRecord> {

    /**
     * 批量插入扫描记录
     *
     * @param scanRecords 扫描记录列表
     * @return 是否插入成功
     */
    boolean batchInsertScanRecords(List<ScaScanRecord> scanRecords);

    /**
     * 根据应用id分页查询扫描记录
     *
     * @param appId 应用id
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 扫描记录分页结果
     */
    Page<ScaScanRecord> getScanRecordsByAppId(Long appId, int pageNum, int pageSize);

    /**
     * 模拟外部接口调用，生成扫描记录数据
     *
     * @param appId 应用id
     * @return 模拟的扫描记录列表
     */
    List<ScaScanRecord> mockScanData(Long appId);

    /**
     * 根据应用id分页查询扫描记录响应
     *
     * @param appId 应用id
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 扫描记录响应分页结果
     */
    Page<ScanRecordResponse> getScanRecordResponsesByAppId(Long appId, int pageNum, int pageSize);
}