package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.db.mapper.thgn.OpenSourceComptBaselineRegisterMapper;
import cn.harmonycloud.development.pojo.vo.thgn.sca.OpenSourceComptBaselineRegister;
import cn.harmonycloud.development.service.OpenSourceComptBaselineRegisterService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class OpenSourceComptBaselineRegisterServiceImpl extends ServiceImpl<OpenSourceComptBaselineRegisterMapper, OpenSourceComptBaselineRegister> implements OpenSourceComptBaselineRegisterService {
}