package cn.harmonycloud.development.inbound.controller.trinasolar;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.entity.thgn.ScaScanRecord;
import cn.harmonycloud.development.pojo.vo.thgn.sca.ScanRecordResponse;
import cn.harmonycloud.development.service.trinasolar.ScanRecordService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扫描记录Controller
 *
 * <AUTHOR>

@Tag(name = "开源组件登记", description = "提供开源组件基线的开源组件扫描等操作")
@RestController
@RequestMapping("/compt-register")
@RequiredArgsConstructor
public class OpenSourceComptRegisterController  {

    @Autowired
    private ScanRecordService scanRecordService;

    /**
     * 模拟扫描数据并插入数据库
     *
     * @param appId 应用id
     * @return 插入结果
     */
    @Operation(summary = "模拟扫描数据并插入数据库", description = "生成模拟的扫描记录数据并插入到数据库中")
    @PostMapping("/base/compt")
    public BaseResult<Boolean> getBaseCompt(
            @Parameter(name = "appId", description = "应用id", required = true, example = "1001")
            @RequestParam Long appId) {
        try {
            // 模拟扫描数据
            List<ScaScanRecord> scanRecords = scanRecordService.mockScanData(appId);
            // 插入数据库
            boolean result = scanRecordService.batchInsertScanRecords(scanRecords);
            return BaseResult.ok(result);
        } catch (Exception e) {
            return BaseResult.failed("模拟扫描数据并插入数据库失败: " + e.getMessage());
        }
    }

    /**
     * 根据应用id分页查询扫描记录
     *
     * @param appId 应用id
     * @param pageNum 页码，默认1
     * @param pageSize 每页数量，默认10
     * @return 扫描记录分页结果
     */
    @Operation(summary = "根据应用id分页查询扫描记录", description = "根据应用id分页查询对应的扫描记录列表")
    @GetMapping("/record")
    public BaseResult<Page<ScanRecordResponse>> getScanRecordsByAppId(
            @Parameter(name = "appId", description = "应用id", required = true, example = "1001")
            @RequestParam Long appId,
            @Parameter(name = "pageNum", description = "页码，默认1", required = false, example = "1")
            @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(name = "pageSize", description = "每页数量，默认10", required = false, example = "10")
            @RequestParam(defaultValue = "10") int pageSize) {
        Page<ScanRecordResponse> scanRecords = scanRecordService.getScanRecordResponsesByAppId(appId, pageNum, pageSize);
        return BaseResult.ok(scanRecords);
    }
}