package cn.harmonycloud.development.inbound.controller.trinasolar;

import cn.harmonycloud.development.pojo.vo.thgn.sca.OpenSourceComptBaselineRegister;
import cn.harmonycloud.development.service.OpenSourceComptBaselineRegisterService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/register/opensource/baseline")
@Tag(name = "开源组件基线管理", description = "开源组件基线CRUD接口")
public class OpenSourceComptBaselineRegisterController {

    @Autowired
    private OpenSourceComptBaselineRegisterService baselineService;

    /**
     * 创建基线
     */
    @PostMapping
    @Operation(summary = "创建开源组件基线")
    public boolean create(@RequestBody OpenSourceComptBaselineRegister baseline) {
        return baselineService.save(baseline);
    }

    /**
     * 查询单个基线
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询单个基线")
    public OpenSourceComptBaselineRegister getById(@PathVariable Long id) {
        return baselineService.getById(id);
    }

    /**
     * 分页查询基线列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询基线列表")
    public Page<OpenSourceComptBaselineRegister> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            OpenSourceComptBaselineRegister query) {
        return baselineService.page(new Page<>(pageNum, pageSize), new QueryWrapper<>(query));
    }

    /**
     * 更新基线
     */
    @PutMapping
    @Operation(summary = "更新基线")
    public boolean update(@RequestBody OpenSourceComptBaselineRegister baseline) {
        return baselineService.updateById(baseline);
    }

    /**
     * 删除基线（逻辑删除）
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除基线")
    public boolean delete(@PathVariable Long id) {
        return baselineService.removeById(id);
    }
}