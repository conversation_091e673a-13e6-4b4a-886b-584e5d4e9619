package cn.harmonycloud.development.inbound.controller.trinasolar;


import cn.harmonycloud.development.config.K8sClusterConfig;
import cn.harmonycloud.development.pojo.dto.thgn.ProgramPodDTO;
import cn.harmonycloud.development.pojo.vo.instance.K8sServiceInstanceVO;
import cn.harmonycloud.development.pojo.vo.instance.PodLogVO;
import cn.harmonycloud.development.pojo.vo.instance.ServiceDeploymentInfoVO;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.common.core.base.BaseResult;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Api(tags = "K8s集群管理")
@RestController
@RequestMapping("/k8s/cluster")
public class K8sClusterManagementController {

    @Autowired
    private K8sServiceInstanceService k8sServiceInstanceService;

    @Autowired
    private K8sClusterConfigService k8sClusterConfigService;


    @Autowired
    private K8sDeploymentService k8sDeploymentService;

    @Autowired
    private K8sLogService k8sLogService;

    @ApiOperation("通过集群信息直接获取服务的所有运行实例")
    @GetMapping("/list-direct")
    public BaseResult<List<K8sServiceInstanceVO>> getServiceInstancesDirect(
            @ApiParam(value = "集群名称") @RequestParam String clusterName,
            @ApiParam(value = "API Server地址") @RequestParam(required = false) String masterUrl,
            @ApiParam(value = "认证Token") @RequestParam(required = false) String token,
            @ApiParam(value = "命名空间") @RequestParam String namespace,
            @ApiParam(value = "服务名称") @RequestParam String serviceName) {

        List<K8sServiceInstanceVO> instances = k8sServiceInstanceService.getServiceInstancesDirect(
                clusterName, masterUrl, token, namespace, serviceName);
        return BaseResult.ok(instances);
    }

    @ApiOperation("验证通过API Server和Token的连接是否有效")
    @GetMapping("/validate/connection")
    public BaseResult<Boolean> validateConnection(
            @ApiParam(value = "API Server地址", required = true) @RequestParam String masterUrl,
            @ApiParam(value = "认证Token", required = true) @RequestParam String token) {
        boolean connection = k8sServiceInstanceService.validateConnection(
                masterUrl, token);
        return BaseResult.ok(connection);
    }

    @ApiOperation("获取集群信息")
    @GetMapping("/config")
    public BaseResult<List<Map<String, Object>>> readK8sClusterConfig(
            @ApiParam(value = "集群名称") @RequestParam(required = false) String clusterName) {
        List<K8sClusterConfig.Cluster> clusters = new ArrayList<>();
        if (!StrUtil.isEmpty(clusterName)) {
            clusters = k8sClusterConfigService.getClusterByName(clusterName);
        } else {
            clusters = k8sClusterConfigService.getAllClusters();
        }
        List<Map<String, Object>> configList = CollUtil.newArrayList();
        //安全保密：不返回token 不返回server
        for (K8sClusterConfig.Cluster cluster : clusters) {
            Map<String, Object> config = MapUtil.newHashMap();
            config.put("name", cluster.getName());
            config.put("env", cluster.getEnv());
            configList.add(config);
        }
        return BaseResult.ok(configList);
    }


    @ApiOperation("从Deployment获取服务的部署信息")
    @GetMapping("/deployment/info/v2")
    public BaseResult<ServiceDeploymentInfoVO> getServiceDeploymentInfoV2(
            @ApiParam(value = "集群名称") @RequestParam String clusterName,
            @ApiParam(value = "API Server地址") @RequestParam(required = false) String masterUrl,
            @ApiParam(value = "认证Token") @RequestParam(required = false) String token,
            @ApiParam(value = "命名空间") @RequestParam String namespace,
            @ApiParam(value = "服务名称") @RequestParam String serviceName) {
        ServiceDeploymentInfoVO deploymentInfo = k8sDeploymentService.getServiceDeploymentInfoFromDeployment(clusterName,
                masterUrl, token, namespace, serviceName);
        return BaseResult.ok(deploymentInfo);
    }


    @ApiOperation("获取指定Pod的日志")
    @GetMapping("/pod")
    public BaseResult<PodLogVO> getPodLogs(
            @ApiParam(value = "集群名称") @RequestParam String clusterName,
            @ApiParam(value = "API Server地址") @RequestParam(required = false) String masterUrl,
            @ApiParam(value = "认证Token") @RequestParam(required = false) String token,
            @ApiParam(value = "命名空间") @RequestParam String namespace,
            @ApiParam(value = "Pod名称") @RequestParam String podName,
            @ApiParam(value = "容器名称") @RequestParam(required = false) String containerName,
            @ApiParam(value = "获取最后多少行日志") @RequestParam(required = false) Integer tailLines,
            @ApiParam(value = "获取最近多少秒的日志") @RequestParam(required = false) Long sinceMinutes) {

        PodLogVO logs = k8sLogService.getPodLogs(clusterName, masterUrl, token, namespace, podName,
                containerName, tailLines, sinceMinutes);
        return BaseResult.ok(logs);
    }

    @ApiOperation("获取系统的命名空间")
    @GetMapping("/application/env/namespace")
    public BaseResult<String> envNamespace(
            @RequestParam(required = true) Long applicationId,
            @RequestParam(required = true) String env) {
        String namespace = k8sLogService.envNamespace(applicationId, env);
        return BaseResult.ok(namespace);
    }


    @ApiOperation("通过应用程序获取对应服务实例个数")
    @PostMapping("/program/env/pod")
    public BaseResult<Map<String, Object>> programEnvPod(
            @RequestBody ProgramPodDTO programPodDTO) {
        Map<String, Object> map = k8sLogService.programEnvPod(programPodDTO);
        return BaseResult.ok(map);
    }
}
