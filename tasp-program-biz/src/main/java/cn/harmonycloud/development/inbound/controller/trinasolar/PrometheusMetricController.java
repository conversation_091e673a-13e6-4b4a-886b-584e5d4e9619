package cn.harmonycloud.development.inbound.controller.trinasolar;


import cn.harmonycloud.development.pojo.vo.instance.CpuMetricsVO;
import cn.harmonycloud.development.pojo.vo.instance.DiskIoMetricsVO;
import cn.harmonycloud.development.pojo.vo.instance.MemoryMetricsVO;
import cn.harmonycloud.development.service.PrometheusCpuMetricsService;
import cn.harmonycloud.development.service.PrometheusDiskIoMetricsService;
import cn.harmonycloud.development.service.PrometheusMemoryMetricsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "K8s集群管理")
@RestController
@RequestMapping("/api/prometheus/")
public class PrometheusMetricController {


    @Autowired
    private PrometheusMemoryMetricsService prometheusMemoryMetricsService;

    @Autowired
    private PrometheusCpuMetricsService prometheusCpuMetricsService;


    @Autowired
    private PrometheusDiskIoMetricsService prometheusDiskIoMetricsService;

    @ApiOperation("获取服务内存使用趋势（通过Prometheus）")
    @GetMapping("/memory/trend")
    MemoryMetricsVO getServiceMemoryTrendFromPrometheus(
            @ApiParam(value = "Prometheus服务器地址", required = true) @RequestParam String prometheusUrl,
            @ApiParam(value = "命名空间", required = true) @RequestParam String namespace,
            @ApiParam(value = "服务名称", required = true) @RequestParam String serviceName,
            @ApiParam(value = "时间范围（分钟），默认60分钟", required = false) @RequestParam(defaultValue = "60") Integer timeRangeMinutes
    ){
        return prometheusMemoryMetricsService.getServiceMemoryTrendFromPrometheus(prometheusUrl, namespace, serviceName, timeRangeMinutes);
    }




    @ApiOperation("获取服务CPU使用率趋势")
    @GetMapping("/cpu/trend")
    CpuMetricsVO getServiceCpuTrend(
            @ApiParam(value = "Prometheus服务器地址", required = true) @RequestParam String prometheusUrl,
            @ApiParam(value = "命名空间", required = true) @RequestParam String namespace,
            @ApiParam(value = "服务名称", required = true) @RequestParam String serviceName,
            @ApiParam(value = "时间范围（分钟），默认60分钟", required = false) @RequestParam(defaultValue = "60") Integer timeRangeMinutes
    ){
        return prometheusCpuMetricsService.getServiceCpuTrendFromPrometheus(prometheusUrl, namespace, serviceName, timeRangeMinutes);
    };


    /**
     * 1:读取  2:写入
     * @param prometheusUrl
     * @param namespace
     * @param serviceName
     * @param timeRangeMinutes
     * @return
     */
    @ApiOperation("获取服务磁盘IO趋势 ")
    @GetMapping("/io/trend")
    DiskIoMetricsVO getServiceDiskIoTrend(
            @ApiParam(value = "Prometheus服务器地址", required = true) @RequestParam String prometheusUrl,
            @ApiParam(value = "命名空间", required = true) @RequestParam String namespace,
            @ApiParam(value = "服务名称", required = true) @RequestParam String serviceName,
            @ApiParam(value = "时间范围（分钟），默认60分钟", required = false) @RequestParam(defaultValue = "60") Integer timeRangeMinutes
    ){

        return prometheusDiskIoMetricsService.getServiceDiskIoTrendFromPrometheus(prometheusUrl, namespace, serviceName, timeRangeMinutes);
    }



}
