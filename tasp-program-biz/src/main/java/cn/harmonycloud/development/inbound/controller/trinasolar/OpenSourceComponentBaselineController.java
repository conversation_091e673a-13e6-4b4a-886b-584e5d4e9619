package cn.harmonycloud.development.inbound.controller.trinasolar;

import cn.harmonycloud.development.pojo.entity.thgn.OpenSourceComponentBaseline;
import cn.harmonycloud.development.pojo.query.OpenSourceComponentBaselineQuery;
import cn.harmonycloud.development.pojo.enums.CategoryEnum;
import cn.harmonycloud.development.service.trinasolar.OpenSourceComponentBaselineService;
import cn.harmonycloud.issue.model.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Tag(name = "开源组件基线管理API", description = "提供开源组件基线的创建、查询、更新和删除等操作")
@RequestMapping("/opensource/baseline")
@RestController
public class OpenSourceComponentBaselineController {

    @Autowired
    private OpenSourceComponentBaselineService openSourceComponentBaselineService;

    @Operation(summary = "创建开源组件基线")
    @PostMapping
    public CommonResult<Boolean> create(@RequestBody OpenSourceComponentBaseline baseline) {
        return CommonResult.success(openSourceComponentBaselineService.saveOpenSourceComponentBaseline(baseline));
    }

    @Operation(summary = "获取开源组件基线详情")
    @Parameter(name = "id", description = "开源组件基线ID", required = true, schema = @Schema(type = "integer"))
    @GetMapping("/{id}")
    public CommonResult<OpenSourceComponentBaseline> getById(@PathVariable Long id) {
        return CommonResult.success(openSourceComponentBaselineService.getById(id));
    }

    @Operation(summary = "更新开源组件基线")
    @PutMapping
    public CommonResult<Boolean> update(@RequestBody OpenSourceComponentBaseline baseline) {
        return CommonResult.success(openSourceComponentBaselineService. updateOpenSourceComponentBaselineById(baseline));
    }

    @Operation(summary = "删除开源组件基线")
    @Parameter(name = "id", description = "开源组件基线ID", required = true, schema = @Schema(type = "Long"))
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> delete(@PathVariable Long id) {
        return CommonResult.success(openSourceComponentBaselineService.removeOpenSourceComponentBaselineById(id));
    }

    @Operation(summary = "获取开源组件基线列表")
    @Parameters({
            @Parameter(name = "pageNo", description = "页码", required = true, schema = @Schema(type = "integer")),
            @Parameter(name = "pageSize", description = "每页条数", required = true, schema = @Schema(type = "integer"))
    })
    @PostMapping("/list")
    public CommonResult<Page<OpenSourceComponentBaseline>> list(
            @RequestBody OpenSourceComponentBaselineQuery query
           ) {
        return CommonResult.success(openSourceComponentBaselineService.pageQuery(query));
    }

    @Operation(summary = "获取所有分类列表")
    @GetMapping("/categories")
    public CommonResult<List<String>> getAllCategories() {
        return CommonResult.success(Arrays.stream(CategoryEnum.values())
                .map(CategoryEnum::getDescription)
                .collect(Collectors.toList()));
    }
}
