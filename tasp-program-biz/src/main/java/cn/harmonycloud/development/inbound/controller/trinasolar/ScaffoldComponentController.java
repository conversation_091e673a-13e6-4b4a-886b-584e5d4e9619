package cn.harmonycloud.development.inbound.controller.trinasolar;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.entity.thgn.ScaffoldComponent;
import cn.harmonycloud.development.service.trinasolar.ScaffoldComponentService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description 脚手架组件管理
 * <AUTHOR>
 * @Date 2025/7/9
 **/
@Api(tags = "脚手架组件管理")
@RequestMapping("/scaffold/component")
@RestController
public class ScaffoldComponentController {

    @Autowired
    private ScaffoldComponentService scaffoldComponentService;

    @Operation(summary = "组件展示")
    @GetMapping("/list")
    public BaseResult<List<ScaffoldComponent>> list() {
        return BaseResult.ok(scaffoldComponentService.list());
    }
}
