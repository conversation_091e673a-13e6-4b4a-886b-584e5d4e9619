package cn.harmonycloud.mybatis.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数据操作接口
 */
public interface BaseRepository<T> extends IService<T> {
    /**
     * 根据对象每个属性搜索
     *
     * @param entity
     * @return
     */
    T getByExample(T entity);

    /**
     * 动态查询list
     *
     * @param query
     * @return
     */
    List<T> getList(BaseQuery<T> query);

    /**
     * 分页动态搜索
     *
     * @param query
     * @return
     */
    Page<T> getListByPage(BaseQuery<T> query);

    /**
     * 批量逻辑删除
     *
     * @param ids
     * @return
     */
    boolean batchDeleteLogic(@NotEmpty List<Serializable> ids);
}
