package cn.harmonycloud.mybatis;

import cn.harmonycloud.mybatis.handler.MyMetaObjectHandler;
import cn.harmonycloud.mybatis.plugins.SqlLogInterceptor;
import cn.harmonycloud.mybatis.single.SingleInsertHelper;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * @Usage: Mybatis配置类
 * @Author: by xiaoyx
 * @Date: 2020/9/16
 */
//@Configuration
//@ConditionalOnBean(DataSource.class)
//@AutoConfigureAfter(DataSourceAutoConfiguration.class)
public class MybatisConfiguration {
    @Value("${project.mybatis-plus.db-type:mysql}")
    private String dbType;
    @Value("${project.fuzzyQuery.enable:false}")
    private Boolean fuzzyQueryEnable;
    @Value("${project.fuzzyQuery.package:cn.harmonycloud}")
    private String fuzzyQueryPackage;
    @Value("${project.singleInsert.enable:false}")
    private Boolean singleInsertEnable;
    @Value("${project.singleInsert.package:cn.harmonycloud}")
    private String singleInsertPackage;

    /**
     * sql 日志
     *
     * @return SqlLogInterceptor
     */
//    @Bean
//    @ConditionalOnProperty(value = "project.mybatis-plus.sql-log", havingValue = "true", matchIfMissing = true)
//    public SqlLogInterceptor sqlLogInterceptor() {
//        return new SqlLogInterceptor();
//    }

//     @Bean
//     public MybatisPlusInterceptor mybatisPlusInterceptor() {
//         MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
//         // 如果用了分页插件注意先 add TenantLineInnerInterceptor 再 add PaginationInnerInterceptor
// //        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new MyTenantLineHandler()));
//         // Tenant修改
// //        interceptor.addInnerInterceptor(new MyTenantLineInnerInterceptor(new SrcTenantLineHandler()));


//         if (BooleanUtils.isTrue(fuzzyQueryEnable) && StringUtils.isNotBlank(fuzzyQueryPackage)) {
//             // todo 多package初始化
//             //FuzzyQueryHelper.init(fuzzyQueryPackage);
//             //interceptor.addInnerInterceptor(new FuzzyQueryInnerInterceptor());
//         }

//         if (BooleanUtils.isTrue(singleInsertEnable) && StringUtils.isNotBlank(singleInsertPackage)) {
//             // todo 多package初始化
//             SingleInsertHelper.init(singleInsertPackage);
//         }
//         // 分页
//         interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.getDbType(dbType)));
//         // 乐观锁
//         //interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
//         return interceptor;
//     }

//    @Bean
//    public MyMetaObjectHandler myMetaObjectHandler() {
//        return new MyMetaObjectHandler();
//    }


}
