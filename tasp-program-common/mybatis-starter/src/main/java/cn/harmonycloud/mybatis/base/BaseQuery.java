package cn.harmonycloud.mybatis.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Usage:
 * @Author: gjq
 * @Date: 2020/3/7
 */
@Data
public class BaseQuery<T> {
    private T search;
    private String[] column;
    private String[] asc = new String[]{};
    private String[] desc = new String[]{};
    private Map<String, String> like = new HashMap<>();
    private List<BetweenEntity> between = new ArrayList<>();
    private int size = 20;
    private int pageNo = 0;

    public BaseQuery() {

    }

    public BaseQuery(T search) {
        this.search = search;
    }

    public static <E> BaseQuery<E> ofSearch(E e) {
        return new BaseQuery(e);
    }

    public Page<T> toPage() {
        return new Page<>(pageNo, size);
    }


//    public Pageable ofPageRequest() {
//        return PageRequest.of(pageNo, size, convertToSort());
//    }
//
//    public Sort convertToSort() {
//        List<Sort.Order> sortValues = new ArrayList<>();
//
//        for (String ascColumn : this.getAsc()) {
//            sortValues.add(new Sort.Order(Sort.Direction.ASC, ascColumn));
//        }
//
//        for (String descColumn : this.getDesc()) {
//            sortValues.add(new Sort.Order(Sort.Direction.DESC, descColumn));
//        }
//        return Sort.by(sortValues);
//    }
}

