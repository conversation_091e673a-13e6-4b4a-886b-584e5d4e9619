package cn.harmonycloud.mybatis.single;

import cn.hutool.core.util.ClassUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 唯一索引的缓存维护类
 */
@Slf4j
public class SingleInsertHelper {
    private static final Map<String, SingleInsert> SINGLE_INSERT_CACHE = new HashMap(12);

    /**
     * 初始化标记SingleInsert注解的查询方法,可以多参数传入
     * TODO 抽象一个包装类，只做一次扫描
     *
     * @param packageName
     */
    public static void init(String... packageName) {
        for (int p = 0; p < packageName.length; p++) {
            Set<Class<?>> fuzzyClass = ClassUtil.scanPackage(packageName[p]);
            fuzzyClass.forEach(c -> {
                String cName = c.getName();
                Method[] methods = c.getMethods();
                for (int m = 0; m < methods.length; m++) {
                    SingleInsert singleInsert = methods[m].getAnnotation(SingleInsert.class);
                    if (singleInsert != null) {
                        String name = cName + "." + methods[m].getName();
                        log.info("初始化Mybatis 唯一索引组件: {},唯一索引的表: {}, 唯一索引的列：{}",
                                name, singleInsert.table(), singleInsert.columns());
                        if (log.isDebugEnabled()) {
                            log.debug("初始化Mybatis 唯一索引组件: {},唯一索引的表: {}, 唯一索引的列：{}",
                                    name, singleInsert.table(), singleInsert.columns());
                        }
                        if (StringUtils.isNotBlank(singleInsert.table()) || singleInsert.columns().length > 1 || StringUtils.isNotBlank(singleInsert.columns()[0])) {
                            SINGLE_INSERT_CACHE.put(name, singleInsert);
                        }
                    }
                }
            });
        }
    }

    /**
     * 判断一个查询是否开启模糊查询缓存
     *
     * @param name
     * @return
     */
    public static boolean isSingleInsertMethod(String name) {
        return SINGLE_INSERT_CACHE.containsKey(name);
    }

    /**
     * 获取列查询参数
     *
     * @param name
     * @return
     */
    public static String[] getColumns(String name) {
        return SINGLE_INSERT_CACHE.get(name).columns();
    }

}
