package cn.harmonycloud.mybatis.handler;

import cn.harmony.sequence.builder.SnowflakeSeqBuilder;
import cn.harmony.sequence.sequence.Sequence;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 全局ID生成器，避免使用原生ID生成
 */
@Slf4j
@Component
public class CustomIdGenerator implements IdentifierGenerator {
    private static final Sequence sequence = SnowflakeSeqBuilder
            .create()
            .datacenterId(1)
            .build();

    @Override
    public Long nextId(Object entity) {
        Long id = sequence.nextValue();
        log.debug("CustomIdGenerator ID {}", id);
        return id;
    }
}