package cn.harmonycloud.mybatis.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.Serializable;
import java.util.List;

/**
 * 基础业务接口
 */
public interface BaseService<T> {

    T getByExample(T entity);

    Page<T> getListByPage(BaseQuery<T> query);

    List<T> getList(BaseQuery<T> query);

    /**
     * 查询列表
     *
     * @return
     */
    List<T> list();

    IPage<T> page(IPage<T> page, T obj);

    T getById(Serializable id);

    boolean save(T obj);

    boolean insert(T obj);

    boolean updateById(T obj);

    boolean removeById(Serializable id);

}
