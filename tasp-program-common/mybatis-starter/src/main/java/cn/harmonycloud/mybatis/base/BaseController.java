package cn.harmonycloud.mybatis.base;

import cn.harmonycloud.common.core.base.BaseEntity;
import cn.harmonycloud.common.core.base.BaseResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Usage: Common Controller（CRUD）
 * @Author: wwyz
 * @Date: 2020/3/2
 */
public abstract class BaseController<S extends BaseService<T>, T extends BaseEntity> {

    @Autowired
    protected S service;

    @ApiOperation(value = "根据ID获取实体")
    @GetMapping("{id}")
    public BaseResult getById(@PathVariable String id) {
        T t = service.getById(Long.valueOf(id));
        BaseResult baseResult = t == null ?
                BaseResult.failed(1, " Record not found") : BaseResult.ok(t);
        return baseResult;
    }

    @ApiOperation(value = "插入实体")
    @PostMapping
    public BaseResult insert(@RequestBody T t) {
        if (t.getId() != null) {
            t.setId(null);
        }
        service.save(t);
        return BaseResult.ok();
    }

    @ApiOperation(value = "修改实体，路径传ID，Body传实体")
    @PutMapping("{id}")
    public BaseResult update(@PathVariable String id, @RequestBody T t) {
        t.setId(Long.valueOf(id));
        service.save(t);
        return BaseResult.ok();
    }

    @ApiOperation(value = "根据id删除实体")
    @DeleteMapping
    public BaseResult delete(String id) {
        service.removeById(id);
        return BaseResult.ok();
    }

    @ApiOperation(value = "单数据条件查询")
    @PostMapping("entity")
    public BaseResult<T> getByExample(@RequestBody T t) {
        BaseResult baseResult = t == null ?
                BaseResult.failed(1, " Record not found") : BaseResult.ok(service.getByExample(t));
        return baseResult;
    }

    @ApiOperation(value = "条件查询一个列表", notes = "使用方式研究query参数的传递")
    @PostMapping("queries")
    public BaseResult<List<T>> getList(@RequestBody BaseQuery<T> query) {
        return BaseResult.ok(service.getList(query));
    }

    @ApiOperation(value = "分页查询", notes = "使用方式研究query参数的传递")
    @PostMapping("page")
    public BaseResult<Page> getListByPage(@RequestBody BaseQuery<T> query) {
        return BaseResult.ok(service.getListByPage(query));
    }
}
