package cn.harmonycloud.mybatis.parser;

import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.mapping.MappedStatement;


/**
 * 自定义JsqlParserSupport，下传MappedStatement
 */
@Slf4j
public abstract class MyJsqlParserSupport extends JsqlParserSupport {

    public String parserSingle(MappedStatement ms, String sql, Object obj) {
        if (log.isDebugEnabled()) {
            log.debug("Original SQL: " + sql);
        }
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            return processParser(ms, statement, 0, obj);
        } catch (JSQLParserException e) {
            throw ExceptionUtils.mpe("Failed to process, Error SQL: %s", e, sql);
        }
    }


    /**
     * 执行 SQL 解析
     *
     * @param statement JsqlParser Statement
     * @return sql
     */
    protected String processParser(MappedStatement ms, Statement statement, int index, Object obj) {
        if (statement instanceof Insert) {
            this.processInsert(ms, (Insert) statement, index, obj);
        } else if (statement instanceof Select) {
            this.processSelect(ms, (Select) statement, index, obj);
        } else if (statement instanceof Update) {
            this.processUpdate(ms, (Update) statement, index, obj);
        } else if (statement instanceof Delete) {
            this.processDelete(ms, (Delete) statement, index, obj);
        }
        final String sql = statement.toString();
        if (log.isDebugEnabled()) {
            log.debug("parser sql: " + sql);
        }
        return sql;
    }


    /**
     * 新增
     */
    protected void processInsert(MappedStatement ms, Insert insert, int index, Object obj) {
        throw new UnsupportedOperationException();
    }

    /**
     * 删除
     */
    protected void processDelete(MappedStatement ms, Delete delete, int index, Object obj) {
        throw new UnsupportedOperationException();
    }

    /**
     * 更新
     */
    protected void processUpdate(MappedStatement ms, Update update, int index, Object obj) {
        throw new UnsupportedOperationException();
    }

    /**
     * 查询
     */
    protected void processSelect(MappedStatement ms, Select select, int index, Object obj) {
        throw new UnsupportedOperationException();
    }
}
