package cn.harmonycloud.mybatis.base;

import cn.harmony.sequence.sequence.Sequence;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.List;

/**
 * 业务封装基础类
 * 常用的方法封装在本类，如需高级mybatis-plus用法使用repository变量操作
 * 建议service依赖repository
 *
 * @param <M> repository
 * @param <T> model
 */
@Validated
public class BaseServiceImpl<M extends BaseRepository<T>, T> implements BaseService<T> {
    @Autowired
    public M repository;
    @Autowired
    protected Sequence sequence;

    @Override
    public T getByExample(T entity) {
        return repository.getByExample(entity);
    }

    @Override
    public Page<T> getListByPage(BaseQuery<T> query) {
        return repository.getListByPage(query);
    }

    @Override
    public List<T> getList(BaseQuery<T> query) {
        return repository.getList(query);
    }

    /**
     * 查询列表
     *
     * @return
     */
    public List<T> list() {
        return repository.list();
    }

    /**
     * 分页查询
     *
     * @param page
     * @param obj
     * @return
     */
    @Override
    public IPage<T> page(IPage<T> page, T obj) {
        return repository.page(page, Wrappers.query(obj));
    }


    /**
     * 通过id查询记录
     *
     * @param id
     * @return
     */
    @Override
    public T getById(Serializable id) {
        return repository.getById(id);
    }

    /**
     * 保存记录，id存在则更新，id不存在则插入
     *
     * @param obj
     * @return
     */
    @Override
    public boolean save(T obj) {
        return repository.save(obj);
    }

    /**
     * 插入记录
     *
     * @param obj
     * @return
     */
    @Override
    public boolean insert(T obj) {
        // todo 设置id
        return repository.save(obj);
    }

    /**
     * 修改记录
     *
     * @param obj
     * @return
     */
    @Override
    public boolean updateById(T obj) {
        return repository.updateById(obj);
    }

    /**
     * 通过id删除记录
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return repository.removeById(id);
    }


}
