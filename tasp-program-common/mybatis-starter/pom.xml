<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.harmonycloud</groupId>
        <artifactId>tasp-program-common</artifactId>
        <version>2.4.0-SNAPSHOT</version>
    </parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
        </plugins>
    </build>
    <modelVersion>4.0.0</modelVersion>
    <version>2.4.0-SNAPSHOT</version>
    <artifactId>mybatis-starter</artifactId>
    <packaging>jar</packaging>

    <properties>
        <jsqlparser.version>5.1</jsqlparser.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <mysql.connector.version>8.3.0</mysql.connector.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>common-core</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--分布式ID-->
        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>sequence-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql.connector.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparser.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>