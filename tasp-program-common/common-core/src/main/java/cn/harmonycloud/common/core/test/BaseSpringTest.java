package cn.harmonycloud.common.core.test;

import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Usage:
 * @Author: gjq
 * @Date: 2019/3/25 5:32 PM
 */
@SpringBootTest
public abstract class BaseSpringTest {
    @Value("${project.unit-test:true}")
    public Boolean unitTest;

    @BeforeEach
    void setUp() {
        initUnitTest();
    }

    public boolean initUnitTest() {
        if (unitTest) {
            initMockMethod();
        }
        return unitTest;
    }

    /**
     * mock的抽象方法
     */
    public abstract void initMockMethod();
}
