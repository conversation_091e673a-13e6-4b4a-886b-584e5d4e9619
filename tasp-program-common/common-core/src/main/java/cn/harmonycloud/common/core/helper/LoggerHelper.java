package cn.harmonycloud.common.core.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * @Usage:
 * @Author: gjq
 * @Date: 2020/3/10
 */
public class LoggerHelper {

    public static void errorWithDebugger(String msg, String description) {
        Logger logger = LoggerFactory.getLogger(new Exception().getStackTrace()[1].getClassName());
        logger.error(msg);
        logger.error(description);
        if (logger.isDebugEnabled()) {
            logger.debug(msg);
            logger.debug(description);
        }
    }

    public static void errorWithDebugger(String msg, String description, Exception e) {
        Logger logger = LoggerFactory.getLogger(new Exception().getStackTrace()[1].getClassName());
        logger.error(msg);
        logger.error(description);
        if (logger.isDebugEnabled()) {
            logger.debug(msg);
            logger.debug("{} and : {}", description, e);
        }
    }

    public static void errorWithDebugger(String content, Exception e) {
        Logger logger = LoggerFactory.getLogger(new Exception().getStackTrace()[1].getClassName());
        logger.error("Error occurred at {}, msg is: {}", new Date(), content);
        if (logger.isDebugEnabled()) {
            logger.debug("Error occurred at {}, msg is: {} and : {}", new Date(), content, e);
        }
    }
}
