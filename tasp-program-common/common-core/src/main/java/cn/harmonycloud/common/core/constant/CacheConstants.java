package cn.harmonycloud.common.core.constant;

/**
 * 缓存的key 常量
 */
public interface CacheConstants {

    /**
     * 全局缓存，在缓存名称上加上该前缀表示该缓存不区分租户，比如:
     * <p/>
     * {@code @Cacheable(value = CacheConstants.GLOBALLY+CacheConstants.MENU_DETAILS, key = "#roleId  + '_menu'", unless = "#result == null")}
     */
    String GLOBALLY = SystemConstants.SYSTEM_PREFIX + "gl:";

    /**
     * 缓存过期时间字段，用法：
     */
    String EXPIRE_TIME_FLAG = "#";

    /**
     * 验证码前缀
     */
    String DEFAULT_CODE_KEY = SystemConstants.SYSTEM_PREFIX + "DEFAULT_CODE_KEY:";


    /**
     * 菜单信息缓存
     */
    String MENU_DETAILS = SystemConstants.SYSTEM_PREFIX + "menu_details";

//    /**
//     * 用户信息缓存
//     */
//    String USER_DETAILS = SystemConstants.SYSTEM_PREFIX + "user_details";

    /**
     * 字典信息缓存
     */
    String DICT_DETAILS = SystemConstants.SYSTEM_PREFIX + "dict_details";


    /**
     * oauth 客户端信息
     */
    String CLIENT_DETAILS_KEY = SystemConstants.SYSTEM_PREFIX + "oauth:client:details";


    /**
     * spring boot admin 事件key
     */
    String EVENT_KEY = SystemConstants.SYSTEM_PREFIX + "event_key";

    /**
     * 路由存放
     */
    String ROUTE_KEY = SystemConstants.SYSTEM_PREFIX + "gateway_route_key";

    /**
     * redis reload 事件
     */
    String ROUTE_REDIS_RELOAD_TOPIC = SystemConstants.SYSTEM_PREFIX + "gateway_redis_route_reload_topic";

    /**
     * 内存reload 时间
     */
    String ROUTE_JVM_RELOAD_TOPIC = SystemConstants.SYSTEM_PREFIX + "gateway_jvm_route_reload_topic";

    /**
     * 参数缓存
     */
    String PARAMS_DETAILS = SystemConstants.SYSTEM_PREFIX + "params_details";

    /**
     * 租户缓存 (不区分租户)
     */
    String TENANT_DETAILS = GLOBALLY + "tenant_details";
}
