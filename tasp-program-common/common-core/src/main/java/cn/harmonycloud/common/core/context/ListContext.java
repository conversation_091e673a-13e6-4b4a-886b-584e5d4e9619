package cn.harmonycloud.common.core.context;

import cn.hutool.core.thread.threadlocal.NamedThreadLocal;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @usage List数据结构的Context实现，Context指能够线程内共享的变量
 * @date 2021/10/5 11:42 下午
 */
public class ListContext {
    private static final ThreadLocal<List> context = new NamedThreadLocal<>("ListContext");

    public static List getContext() {
        List list = context.get();
        if (list == null) {
            list = new ArrayList();
            context.set(list);
        }
        return list;
    }

    public static void setContext(List list) {
        context.set(list);
    }

    public static void clear() {
        context.remove();
    }

    public static boolean add(Object value) {
        return getContext().add(value);
    }

    public static void add(int i, Object value) {
        getContext().add(i, value);
    }

    public Object get(int i) {
        return getContext().get(i);
    }
}
