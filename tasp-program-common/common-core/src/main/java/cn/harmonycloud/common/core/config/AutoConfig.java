package cn.harmonycloud.common.core.config;

import cn.harmonycloud.common.core.annotation.ExceptionRetryAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Usage: 配置Bean
 */
@Configuration
public class AutoConfig {

    /**
     * 重试切面
     *
     * @return
     */
    @Bean
    public ExceptionRetryAspect exceptionRetryAspect() {
        return new ExceptionRetryAspect();
    }
}
