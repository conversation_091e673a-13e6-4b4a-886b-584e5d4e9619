package cn.harmonycloud.common.core.config;

import org.springframework.core.annotation.Order;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;

@ControllerAdvice
@Order(100)
public class GlobalControllerAdvice {

    @InitBinder
    public void setAllowedFields(WebDataBinder webDataBinder){
        String[] abd = new String[]{"class.*","Class.*","*.class.*","*.Class.*"};
        webDataBinder.setDisallowedFields(abd);
    }

}
