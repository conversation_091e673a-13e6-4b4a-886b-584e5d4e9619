package cn.harmonycloud.common.core.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * @Usage: 实体基类，封装通用字段，真实删除
 */
@Getter
@Setter
public class BaseEntityRightDel implements Serializable {

    @TableId
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @ApiModelProperty(value = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 检查列名是否是公共
     * @param columnName
     * @return
     */
    public static boolean checkCommonColumn(String columnName) {
        Set<String> commonColumns = Sets.newHashSetWithExpectedSize(6);
        commonColumns.add("ID");
        commonColumns.add("CREATE_TIME");
        commonColumns.add("CREATE_BY");
        commonColumns.add("UPDATE_TIME");
        commonColumns.add("UPDATE_BY");
        return commonColumns.contains(columnName.toUpperCase());
    }
}
