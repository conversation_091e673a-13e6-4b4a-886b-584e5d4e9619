<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.harmonycloud</groupId>
        <artifactId>tasp-program-common</artifactId>
        <version>2.4.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>2.4.0-SNAPSHOT</version>
    <artifactId>tenant-starter</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <!--核心工具包-->
        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>common-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.harmonycloud</groupId>
            <artifactId>mybatis-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
    </dependencies>

</project>