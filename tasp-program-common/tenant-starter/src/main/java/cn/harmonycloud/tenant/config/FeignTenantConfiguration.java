//package cn.harmonycloud.tenant.config;
//
//import cn.harmonycloud.tenant.interceptor.FeignTenantInterceptor;
//import feign.RequestInterceptor;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * feign 租户信息拦截
// */
//@Configuration
//public class FeignTenantConfiguration {
//    @Bean
//    public RequestInterceptor feignTenantInterceptor() {
//        return new FeignTenantInterceptor();
//    }
//}
