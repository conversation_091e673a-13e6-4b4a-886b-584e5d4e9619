package cn.harmonycloud.tenant;

import cn.harmonycloud.tenant.user.UserInfo;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 租户工具类
 */
@UtilityClass
public class TenantContextHolder {

    private final ThreadLocal<String> THREAD_LOCAL_TENANT = new TransmittableThreadLocal<>();
    private final ThreadLocal<String> THREAD_LOCAL_TOKEN = new TransmittableThreadLocal<>();
    private final ThreadLocal<UserInfo> THREAD_LOCAL_USER_INFO = new TransmittableThreadLocal<>();
    private final ThreadLocal<String> THREAD_LOCAL_RESOURCE  = new TransmittableThreadLocal<>();


    /**
     * 获取TTL中的租户ID
     *
     * @return
     */
    public String getTenantId() {
        return THREAD_LOCAL_TENANT.get();
    }

    /**
     * TTL 设置租户ID
     *
     * @param tenantId
     */
    public void setTenantId(String tenantId) {
        THREAD_LOCAL_TENANT.set(tenantId);
    }


    /**
     * 获取Token
     * @return
     */
    public String getToken(){
        return THREAD_LOCAL_TOKEN.get();
    }

    /**
     * 设置Token
     * @param token
     */
    public void setToken(String token) {
        THREAD_LOCAL_TOKEN.set(token);
    }

    /**
     * 获取用户信息
     * @return
     */
    public UserInfo getUserInfo(){
        return THREAD_LOCAL_USER_INFO.get();
    }

    /**
     * 设置用户信息
     * @param userInfo
     */
    public void setUserInfo(String userInfo) {
        if(StringUtils.isNotBlank(userInfo)) {
            THREAD_LOCAL_USER_INFO.set(JSONObject.parseObject(userInfo,UserInfo.class));
        }
    }

    /**
     * 获取资源信息
     * @return
     */
    public String getResource(){
        return THREAD_LOCAL_RESOURCE.get();
    }

    /**
     * 设置资源信息
     * @param resource
     */
    public void setResource(String resource){
        if(StringUtils.isNotBlank(resource)) {
            THREAD_LOCAL_RESOURCE.set(resource);
        }
    }

    /**
     * 获取TTL中的用户名
     *
     * @return
     */
    public String getUsername() {
        if(ObjectUtils.isEmpty(getUserInfo())) {
            return "";
        }
        String username = getUserInfo().getUsername();
        return StringUtils.isEmpty(username) ? "" : username;
    }

    /**
     * 获取TTL中的用户ID
     *
     * @return
     */
    public Long getUserId(){
        if(ObjectUtils.isEmpty(getUserInfo())) {
            return null;
        }
        return getUserInfo().getId();
    }


    public void clear() {
        THREAD_LOCAL_TENANT.remove();
        THREAD_LOCAL_USER_INFO.remove();
        THREAD_LOCAL_TOKEN.remove();
        THREAD_LOCAL_RESOURCE.remove();
    }


}
