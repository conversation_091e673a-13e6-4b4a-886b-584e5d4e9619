package cn.harmonycloud.tenant.mybatis;

import cn.harmonycloud.tenant.config.TenantConfigProperties;
import cn.harmonycloud.tenant.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.NullValue;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.StringUtils;

/**
 * 租户维护处理器
 */
@Slf4j
public class MybatisTenantHandler implements TenantLineHandler {


    private  final TenantConfigProperties properties;

    public MybatisTenantHandler(TenantConfigProperties tenantConfigProperties) {
        this.properties = tenantConfigProperties;
    }

    /**
     * 获取租户值
     * <p>
     * TODO 校验租户状态
     *
     * @return 租户值
     */
    @Override
    public Expression getTenantId() {
        String tenantId = TenantContextHolder.getTenantId();
        log.debug("当前租户为 >> {}", tenantId);

        if (tenantId == null) {
            return new NullValue();
        }
        if(StringUtils.equals(properties.getTenantFieldType(),TenantConfigProperties.FIELD_TYPE_LONG)) {
            return new LongValue(tenantId);
        }
        return new StringValue(tenantId);
    }


    /**
     * 获取租户字段名
     *
     * @return 租户字段名
     */
    @Override
    public String getTenantIdColumn() {
        return properties.getTenantColumn();
    }


    /**
     * 根据表名判断是否进行过滤
     *
     * @param tableName 表名
     * @return 是否进行过滤
     */
    @Override
    public boolean ignoreTable(String tableName) {
        String tenantId = TenantContextHolder.getTenantId();
        // 租户中ID 为空，查询全部，不进行过滤
        if (StringUtils.isEmpty(tenantId)) {
            return Boolean.TRUE;
        }
        if(properties.getAllTenantTables()){
            return Boolean.FALSE;
        }

        return !properties.getTenantTables().contains(tableName);
    }
}
