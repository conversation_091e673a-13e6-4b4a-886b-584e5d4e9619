package cn.harmonycloud.tenant.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * 多租户配置
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "hc-tenant.biz")
public class TenantConfigProperties {

    public static String FIELD_TYPE_LONG = "Long";

    public static String FIELD_TYPE_STRING = "String";

    /**
     * 维护租户列名称
     */
    private String tenantColumn = "tenant_id";

    /**
     * 租户字段类型 支持Long、String
     */
    private String tenantFieldType = "Long";

    /**
     * 全部表都是多租户
     */
    private Boolean allTenantTables = true;

    /**
     * 多租户的数据表集合
     */
    private List<String> tenantTables = new ArrayList<>();


    /**
     * 需要校验的header必填key值
     */
    private List<String> checkHeaderKey = new ArrayList<>();
}
