//package cn.harmonycloud.tenant.filter;
//
//import cn.harmonycloud.common.core.constant.SecurityConstants;
//import cn.harmonycloud.common.core.exception.BusinessException;
//import cn.harmonycloud.tenant.config.TenantConfigProperties;
//import cn.harmonycloud.tenant.TenantContextHolder;
//import cn.hutool.core.util.StrUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.Ordered;
//import org.springframework.core.annotation.Order;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Component;
//import org.springframework.web.filter.GenericFilterBean;
//
//import jakarta.servlet.FilterChain;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.ServletRequest;
//import jakarta.servlet.ServletResponse;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
///**
// * 租户上下文
// */
//@Slf4j
//@Component
//@Order(Ordered.HIGHEST_PRECEDENCE)
//public class TenantContextHolderFilter extends GenericFilterBean {
//
//    @Autowired
//    private TenantConfigProperties properties;
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        HttpServletResponse response = (HttpServletResponse) servletResponse;
//
//        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
//        String tenantId = request.getHeader(SecurityConstants.TENANT_ID);
//        String userInfo = request.getHeader(SecurityConstants.MICRO_USER_INFO);
//        String resource = request.getHeader(SecurityConstants.RESOURCE);
//        log.debug("获取header中信息 token: {}, 租户ID: {}, 用户信息: {}, 资源信息: {}", token, tenantId, userInfo, resource);
//        if(properties.getCheckHeaderKey().contains(HttpHeaders.AUTHORIZATION) && StrUtil.isBlank(token)) {
//            throw new BusinessException("token is not null");
//        }
//        TenantContextHolder.setToken(token);
//        if(properties.getCheckHeaderKey().contains(SecurityConstants.TENANT_ID) && StrUtil.isBlank(tenantId)) {
//            throw new BusinessException(SecurityConstants.TENANT_ID +" is not null");
//        }
//        TenantContextHolder.setTenantId(tenantId);
//        if(properties.getCheckHeaderKey().contains(SecurityConstants.MICRO_USER_INFO) && StrUtil.isBlank(userInfo)) {
//            throw new BusinessException(SecurityConstants.MICRO_USER_INFO +" is not null");
//        }
//        TenantContextHolder.setUserInfo(userInfo);
//        if(properties.getCheckHeaderKey().contains(SecurityConstants.RESOURCE) && StrUtil.isBlank(resource)) {
//            throw new BusinessException(SecurityConstants.RESOURCE +" is not null");
//        }
//        TenantContextHolder.setResource(resource);
//        try {
//            filterChain.doFilter(request, response);
//        } finally {
//            TenantContextHolder.clear();
//        }
//
//    }
//}
