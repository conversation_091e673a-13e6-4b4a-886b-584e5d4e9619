package cn.harmonycloud.common.redis.component;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import com.alibaba.fastjson.JSON;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Usage:
 * @Author: gjq
 * @Date: 2020/9/30
 */
public class RedisClientComponent {
    private final RedisTemplate<String, Object> redisTemplate;

    public RedisClientComponent(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        defaultInitRedisTemplate();
    }

    /**
     * 暴露redisTemplate，可进一步配置属性
     * @return
     */
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

    /**
     * ************************************************************************
     * Key                                  *
     * ************************************************************************
     */

    /**
     * 判断Redis中是否有key
     * @param key
     * @return Redis中是否有key
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * TTL key，以秒为单位
     * @param key
     * @return 给定 key的剩余生存时间
     */
    public long ttl(String key){
        return redisTemplate.getExpire(key);
    }

    /**
     * 刷新key的失效时间
     * @param key
     * @param expire
     * @param timeUnit
     */
    public void expiteKey(String key, long expire, TimeUnit timeUnit) {
        redisTemplate.expire(key, expire, timeUnit);
    }

    /**
     * INCR key，key增加一次
     * @param key,delta
     * @return 加上指定的增量值之后， key 的值
     */
    public long incr(String key,long delta){
        return redisTemplate.opsForValue().increment(key,delta);
    }

    /**
     * KEYS pattern
     * @param pattern
     * @return 符合给定模式pattern的的 key 列表 (Array)。
     */
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 批量删除缓存
     */
    public void delBatchKeys(String pattern){
        Set<String> keys = redisTemplate.keys(pattern+"*");
        redisTemplate.delete(keys);
    }

    /**
     * 删除key
     * @param key
     * @return
     */
    public Boolean del(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * ************************************************************************
     * String                                  *
     * ************************************************************************
     */


    /**
     * 添加字符串KEY，不设失效时间
     * @param key
     * @param value
     */
    public void addKey(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 添加字符串KEY
     * @param key      字符串KEY
     * @param value    值可以为对象
     * @param expire   过期时间
     * @param timeUnit TimeUnit中定义的时间单位
     */
    public void addKey(String key, Object value, long expire, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, expire, timeUnit);
    }

    /**
     * 将输入的对象转化为json存储
     *
     * @param key
     * @param value
     */
    public void addJson(String key, Object value) {
        redisTemplate.opsForValue().set(key, JSON.toJSONString(value));
    }

    /**
     * 将redis的str转换为对象返回
     *
     * @param key
     * @return
     */
    public <T> T getJson(String key, Class<T> clazz) {
        String str = (String) redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return JSON.parseObject(str, clazz);
    }
    /**
     * GET key
     * @param key
     * @return key所关联的字符串值
     */
    public String get(String key) {
        return (String)redisTemplate.opsForValue().get(key);
    }

    /**
     * 批量查询，对应mget
     * @param keys
     * @return 返回所有(一个或多个)给定 key 的值。 如果给定的 key 里面，有某个 key 不存在，那么这个 key 返回特殊值 nil 。
     */
    public List<Object> mget(List<String> keys){
        return redisTemplate.opsForValue().multiGet(keys);
    }

    /**
     * 批量查询，管道pipeline
     * @param keys
     * @return 批量查询的结果
     */
    public  List<Object> batchGet(List<String> keys){
        List<Object> result = redisTemplate.executePipelined(new RedisCallback<String>() {
            @Override
            public String doInRedis(RedisConnection connection) throws DataAccessException {
                StringRedisConnection src = (StringRedisConnection) connection;
                for (String k : keys) {
                    src.get(k);
                }
                return null;
            }
        });
        return result;
    }


    /**
     * ************************************************************************
     * HashMap                                  *
     * ************************************************************************
     */

    public HashOperations setHashOperations(RedisSerializer<?> hashValueSerializer) {
        this.redisTemplate.setHashValueSerializer(hashValueSerializer);
        return redisTemplate.opsForHash();
    }

    /**
     * HGETALL key
     * @param key
     * @return 哈希表 key中，所有的域和值。
     */
    public Map<Object, Object> mapGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 判断Map中是否包含Key
     *
     * @param key
     * @return
     */
    public Boolean mapHasKey(String key, String hashKey) {
        return redisTemplate.opsForHash().hasKey(key, hashKey);
    }

    /**
     * 获取hashmap
     *
     * @param key
     * @return
     */
    public <T> T mapGet(String key, String hashKey) {
        return (T) redisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 获取hashmap
     *
     * @param key
     * @return
     */
    public <T> T mapGet(String key, String hashKey, RedisSerializer<?> hashValueSerializer) {
        this.redisTemplate.setHashValueSerializer(hashValueSerializer);
        return (T) redisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 获取map keys
     * @param key
     * @return
     */
    public Set mapGetKeys(String key) {
        return redisTemplate.opsForHash().keys(key);
    }

    /**
     * map put操作
     * @param key
     * @param hashKey
     * @param value
     * @param <T>
     */
    public <T> void mapPut(String key, String hashKey, T value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * map put 如果不存在则添加
     * @param key
     * @param hashKey
     * @param value
     * @param <T>
     */
    public <T> void mapPutIfAbsent(String key, String hashKey, T value) {
        redisTemplate.opsForHash().putIfAbsent(key, hashKey, value);
    }

    /**
     * map delete操作
     *
     * @param hashKey
     * @param value
     * @param <T>
     */
    public <T> void mapDelete(String hashKey, T value) {
        redisTemplate.opsForHash().delete(hashKey, value);
    }

    /**
     * ************************************************************************
     * List                                  *
     * ************************************************************************
     */

    /**
     * 通过索引获取列表中的元素
     * @param key
     * @param index
     * @return 该索引在列表中对应的元素
     */
    public String lIndex(String key, long index) {
        return (String) redisTemplate.opsForList().index(key, index);
    }

    /**
     * 获取列表指定范围内的元素
     * @param key
     * @param start
     *            开始位置, 0是开始位置
     * @param end
     *            结束位置, -1返回所有
     * @return
     */
    public List<Object> lRange(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * LPUSH key value，将一个值 value插入到列表 key的表头
     * @param key
     * @param value
     * @return 执行 LPUSH命令后，列表的长度。
     */
    public long lpush(String key , String value){
        return redisTemplate.opsForList().leftPush(key,value);
    }

    /**
     * 实现命令：LPOP key，移除集合中的左边第一个元素并返回列表 key的头元素。
     * @param key
     * @return 列表key的头元素,当列表 key 不存在时，返回 nil
     */
    public String lpop(String key){
        return (String)redisTemplate.opsForList().leftPop(key);
    }

    /**
     * 移出并获取列表的第一个元素， 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止
     * @param key
     * @param timeout
     *            等待时间
     * @param unit
     *            时间单位
     * @return
     */
    public String lBLeftPop(String key, long timeout, TimeUnit unit) {
        return (String) redisTemplate.opsForList().leftPop(key, timeout, unit);
    }

    /**
     * 实现命令：RPUSH key value，将一个值 value 插入到列表 key的表尾(最右边)。
     * @param key
     * @param value
     * @return 执行 LPUSH命令后，列表的长度。
     */
    public long rpush(String key,String value){
        return redisTemplate.opsForList().rightPush(key,value);
    }

    /**
     * 实现命令：RPOP key，移除集合中的右边第一个元素。
     * @param key
     * @return 返回值为移除的元素
     */
    public String rpop(String key){
        return (String)redisTemplate.opsForList().rightPop(key);
    }

    /**
     * 移出并获取列表的最后一个元素， 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止
     * @param key
     * @param timeout
     *            等待时间
     * @param unit
     *            时间单位
     * @return
     */
    public String lBRightPop(String key, long timeout, TimeUnit unit) {
        return (String) redisTemplate.opsForList().rightPop(key, timeout, unit);
    }

    /**
     * 获取map的结果集，返回List
     * eg: redisClientComponent.mapGetValues(CacheConstants.ROUTE_KEY,new Jackson2JsonRedisSerializer<>(RouteDefinitionVo.class));
     * @param key
     * @param hashValueSerializer
     * @return
     */
    public List mapGetValues(String key, RedisSerializer<?> hashValueSerializer) {
        this.redisTemplate.setHashValueSerializer(hashValueSerializer);
        return redisTemplate.opsForHash().values(key);
    }

    /**
     *************************************************************************
     *                              HashSet                                  *
     *************************************************************************
     */

    /**
     * 判断hashset中是否存在setValue
     *
     * @param key
     * @param setValue
     * @return boolean
     */
    public Boolean hasSetKey(String key, Object setValue) {
        return redisTemplate.opsForSet().isMember(key, setValue);
    }

    /**
     * 获取set
     *
     * @param key
     * @return
     */
    public Set getSet(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 将set设置到redis的set中
     *
     * @param key
     * @param value
     * @return
     */
    public Long addSet(String key, Set value) {
        return redisTemplate.opsForSet().add(key, value.toArray());
    }

    /**
     * 获取集合的大小
     *
     * @param key
     * @return
     */
    public Long sSize(String key) {
        return redisTemplate.opsForSet().size(key);
    }

    /**
     * 判断集合是否包含value
     *
     * @param key
     * @param value
     * @return
     */
    public Boolean sIsMember(String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /**
     *************************************************************************
     *                             ZSet                                  *
     *************************************************************************
     */

    /**
     * 添加元素,有序集合是按照元素的score值由小到大排列
     *
     * @param key,value,score
     * @return
     */
    public Boolean zAdd(String key, String value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 获取集合的元素, 从小到大排序
     *
     * @param key
     * @param start
     *            开始位置
     * @param end
     *            结束位置, -1查询所有
     * @return
     */
    public Set<Object> zRange(String key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    /**
     *************************************************************************
     *                             Publisher                                  *
     *************************************************************************
     */

    /**
     * 向指定频道发布消息
     * @param channel 频道名称
     * @param value 消息
     */
    public void publish(String channel,String value){
        redisTemplate.convertAndSend(channel,value);
    }

    private void defaultInitRedisTemplate(){
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
    }
}
